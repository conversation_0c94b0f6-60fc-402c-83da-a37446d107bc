<?php

namespace Tests\Unit\Services\Sales\Ceiling\Strategies\Distribution;

use PHPUnit\Framework\TestCase;

/**
 * Test rollback functionality in StoreStrategy
 * 
 * These tests verify that the StoreStrategy properly uses transactions
 * to ensure data integrity when distribution failures occur.
 */
class StoreStrategyRollbackTest extends TestCase
{
    /**
     * Test that StoreStrategy has transaction wrapper in processCeilingSale method
     */
    public function test_store_strategy_uses_transaction_wrapper(): void
    {
        // This test verifies that the StoreStrategy class has been modified
        // to include transaction handling in the processCeilingSale method
        
        $reflection = new \ReflectionClass(\App\Services\Sales\Ceiling\Strategies\Distribution\StoreStrategy::class);
        $method = $reflection->getMethod('processCeilingSale');
        
        // Get the method source code
        $filename = $reflection->getFileName();
        $startLine = $method->getStartLine();
        $endLine = $method->getEndLine();
        
        $lines = file($filename);
        $methodCode = implode('', array_slice($lines, $startLine - 1, $endLine - $startLine + 1));
        
        // Assert that the method contains transaction handling
        $this->assertStringContainsString('executeInTransaction', $methodCode, 
            'StoreStrategy::processCeilingSale should use transaction wrapper');
        
        $this->assertStringContainsString('try', $methodCode,
            'StoreStrategy::processCeilingSale should have try-catch for exception handling');
    }

    /**
     * Test that StoreStrategy has internal processing method
     */
    public function test_store_strategy_has_internal_processing_method(): void
    {
        $reflection = new \ReflectionClass(\App\Services\Sales\Ceiling\Strategies\Distribution\StoreStrategy::class);
        
        // Check if the internal processing method exists
        $this->assertTrue($reflection->hasMethod('processCeilingSaleInternal'),
            'StoreStrategy should have processCeilingSaleInternal method for transaction handling');
        
        $method = $reflection->getMethod('processCeilingSaleInternal');
        $this->assertTrue($method->isPrivate(),
            'processCeilingSaleInternal should be private');
    }

    /**
     * Test that StoreStrategy has validation integrity method
     */
    public function test_store_strategy_has_validation_integrity_method(): void
    {
        $reflection = new \ReflectionClass(\App\Services\Sales\Ceiling\Strategies\Distribution\StoreStrategy::class);
        
        // Check if the validation method exists
        $this->assertTrue($reflection->hasMethod('validateDistributionIntegrity'),
            'StoreStrategy should have validateDistributionIntegrity method');
        
        $method = $reflection->getMethod('validateDistributionIntegrity');
        $this->assertTrue($method->isPrivate(),
            'validateDistributionIntegrity should be private');
    }

    /**
     * Test that createAndDistributeFullQuantitySale method has enhanced error handling
     */
    public function test_create_and_distribute_method_has_enhanced_error_handling(): void
    {
        $reflection = new \ReflectionClass(\App\Services\Sales\Ceiling\Strategies\Distribution\StoreStrategy::class);
        $method = $reflection->getMethod('createAndDistributeFullQuantitySale');
        
        // Get the method source code
        $filename = $reflection->getFileName();
        $startLine = $method->getStartLine();
        $endLine = $method->getEndLine();
        
        $lines = file($filename);
        $methodCode = implode('', array_slice($lines, $startLine - 1, $endLine - $startLine + 1));
        
        // Assert that the method contains enhanced error handling
        $this->assertStringContainsString('validateDistributionIntegrity', $methodCode,
            'createAndDistributeFullQuantitySale should call validateDistributionIntegrity');
        
        $this->assertStringContainsString('try', $methodCode,
            'createAndDistributeFullQuantitySale should have try-catch blocks');
    }

    /**
     * Test that SaleDetailFactory has enhanced error handling
     */
    public function test_sale_detail_factory_has_enhanced_error_handling(): void
    {
        $reflection = new \ReflectionClass(\App\Services\Sales\Ceiling\Strategies\Distribution\Services\SaleDetailFactory::class);
        $method = $reflection->getMethod('insertDetails');
        
        // Get the method source code
        $filename = $reflection->getFileName();
        $startLine = $method->getStartLine();
        $endLine = $method->getEndLine();
        
        $lines = file($filename);
        $methodCode = implode('', array_slice($lines, $startLine - 1, $endLine - $startLine + 1));
        
        // Assert that the method contains enhanced error handling
        $this->assertStringContainsString('validateDetailsBeforeInsertion', $methodCode,
            'SaleDetailFactory::insertDetails should validate details before insertion');
        
        $this->assertStringContainsString('try', $methodCode,
            'SaleDetailFactory::insertDetails should have try-catch blocks');
        
        $this->assertStringContainsString('Log::error', $methodCode,
            'SaleDetailFactory::insertDetails should log errors for empty details');
    }

    /**
     * Test that SplitDistributionAlgorithm has enhanced error reporting
     */
    public function test_split_distribution_algorithm_has_enhanced_error_reporting(): void
    {
        $reflection = new \ReflectionClass(\App\Services\Sales\Ceiling\Strategies\Distribution\Algorithms\SplitDistributionAlgorithm::class);
        $method = $reflection->getMethod('distributeExcessSale');
        
        // Get the method source code
        $filename = $reflection->getFileName();
        $startLine = $method->getStartLine();
        $endLine = $method->getEndLine();
        
        $lines = file($filename);
        $methodCode = implode('', array_slice($lines, $startLine - 1, $endLine - $startLine + 1));
        
        // Assert that the method contains enhanced error reporting
        $this->assertStringContainsString('possible_causes', $methodCode,
            'SplitDistributionAlgorithm::distributeExcessSale should log possible causes for failures');
        
        $this->assertStringContainsString('try', $methodCode,
            'SplitDistributionAlgorithm::distributeExcessSale should have try-catch blocks');
    }
}
