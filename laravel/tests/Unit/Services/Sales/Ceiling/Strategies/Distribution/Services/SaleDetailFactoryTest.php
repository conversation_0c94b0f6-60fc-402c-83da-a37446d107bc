<?php

namespace Tests\Unit\Services\Sales\Ceiling\Strategies\Distribution\Services;

use App\Sale;
use App\SaleDetail;
use App\Services\Sales\Ceiling\Strategies\Distribution\Services\SaleDetailFactory;
use Tests\TestCase;
use Mockery;

/**
 * Test class for SaleDetailFactory
 *
 * Tests the sale detail factory service that creates sale details for limited sales
 * and from distribution ratios, and handles bulk insertion
 *
 * @covers \App\Services\Sales\Ceiling\Strategies\Distribution\Services\SaleDetailFactory
 */
class SaleDetailFactoryTest extends TestCase
{
    private SaleDetailFactory $saleDetailFactory;

    protected function setUp(): void
    {
        parent::setUp();
        $this->saleDetailFactory = new SaleDetailFactory();
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * Test create limited sale details success
     */
    public function test_create_limited_sale_details_success(): void
    {
        // Arrange
        $originalSale = $this->createMockOriginalSale();
        $limitedSale = $this->createMockLimitedSale();
        $ceilingSale = (object) ['date' => '2023-01-01'];

        $expectedDetails = [
            [
                'line_id' => 1,
                'div_id' => 10,
                'brick_id' => 100,
                'quantity' => 30.0, // 50 * 0.6
                'bonus' => 15.0,   // 25 * 0.6
                'value' => 300.0,  // 500 * 0.6
                'date' => '2023-01-01',
                'sale_id' => 2,
                'file_id' => 'file1',
                'ratio' => 0.6,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'line_id' => 2,
                'div_id' => 20,
                'brick_id' => 200,
                'quantity' => 20.0, // 50 * 0.4
                'bonus' => 10.0,   // 25 * 0.4
                'value' => 200.0,  // 500 * 0.4
                'date' => '2023-01-01',
                'sale_id' => 2,
                'file_id' => 'file2',
                'ratio' => 0.4,
                'created_at' => now(),
                'updated_at' => now()
            ]
        ];

        // Mock SaleDetail::insert using alias mock
        $saleDetailMock = Mockery::mock('alias:' . SaleDetail::class);
        $saleDetailMock->shouldReceive('insert')
            ->once()
            ->with(Mockery::on(function ($details) use ($expectedDetails) {
                // Check structure without exact timestamp matching
                return count($details) === 2 &&
                       $details[0]['line_id'] === 1 &&
                       $details[0]['quantity'] === 30.0 &&
                       $details[1]['line_id'] === 2 &&
                       $details[1]['quantity'] === 20.0;
            }))
            ->andReturn(true);

        // Act
        $result = $this->saleDetailFactory->createLimitedSaleDetails($originalSale, $limitedSale, $ceilingSale);

        // Assert
        $this->assertTrue($result);
    }

    /**
     * Test create limited sale details with zero original quantity
     */
    public function test_create_limited_sale_details_with_zero_original_quantity(): void
    {
        // Arrange
        $originalSale = $this->createMockOriginalSaleWithZeroQuantity();
        $limitedSale = $this->createMockLimitedSale();
        $ceilingSale = (object) ['date' => '2023-01-01'];

        // Mock SaleDetail::insert using alias mock
        $saleDetailMock = Mockery::mock('alias:' . SaleDetail::class);
        $saleDetailMock->shouldReceive('insert')
            ->once()
            ->withAnyArgs() // Using withAnyArgs for this test due to alias mock complexity
            ->andReturn(true);

        // Act
        $result = $this->saleDetailFactory->createLimitedSaleDetails($originalSale, $limitedSale, $ceilingSale);

        // Assert
        $this->assertTrue($result);
    }

    /**
     * Test create details from ratios with default multipliers
     */
    public function test_create_details_from_ratios_with_defaults(): void
    {
        // Arrange
        $sale = $this->createMockSale();
        $distributionRatios = [
            (object) [
                'line_id' => 1,
                'div_id' => 10,
                'brick_id' => 100,
                'percentage' => 0.6
            ],
            (object) [
                'line_id' => 2,
                'div_id' => 20,
                'brick_id' => 200,
                'percentage' => 0.4
            ]
        ];

        // Act
        $result = $this->saleDetailFactory->createDetailsFromRatios($sale, $distributionRatios);

        // Assert
        $this->assertCount(2, $result);

        $this->assertEquals(1, $result[0]['sale_id']);
        $this->assertEquals(1, $result[0]['line_id']);
        $this->assertEquals(10, $result[0]['div_id']);
        $this->assertEquals(100, $result[0]['brick_id']);
        $this->assertEquals(60.0, $result[0]['quantity']); // 100 * 0.6 * 1.0
        $this->assertEquals(30.0, $result[0]['bonus']);    // 50 * 0.6 * 1.0
        $this->assertEquals(600.0, $result[0]['value']);   // 1000 * 0.6 * 1.0
        $this->assertEquals(0.6, $result[0]['ratio']);     // 0.6 * 1.0

        $this->assertEquals(40.0, $result[1]['quantity']); // 100 * 0.4 * 1.0
        $this->assertEquals(20.0, $result[1]['bonus']);    // 50 * 0.4 * 1.0
        $this->assertEquals(400.0, $result[1]['value']);   // 1000 * 0.4 * 1.0
        $this->assertEquals(0.4, $result[1]['ratio']);     // 0.4 * 1.0
    }

    /**
     * Test create details from ratios with custom multipliers
     */
    public function test_create_details_from_ratios_with_custom_multipliers(): void
    {
        // Arrange
        $sale = $this->createMockSale();
        $distributionRatios = [
            (object) [
                'line_id' => 1,
                'div_id' => 10,
                'brick_id' => 100,
                'percentage' => 0.5
            ]
        ];

        // Act
        $result = $this->saleDetailFactory->createDetailsFromRatios(
            $sale,
            $distributionRatios,
            0.9, // quantityMultiplier
            0.8, // bonusMultiplier
            0.7, // valueMultiplier
            0.6  // ratioMultiplier
        );

        // Assert
        $this->assertCount(1, $result);
        $this->assertEquals(45.0, $result[0]['quantity']); // 100 * 0.5 * 0.9
        $this->assertEquals(20.0, $result[0]['bonus']);    // 50 * 0.5 * 0.8
        $this->assertEquals(350.0, $result[0]['value']);   // 1000 * 0.5 * 0.7
        $this->assertEquals(0.3, $result[0]['ratio']);     // 0.5 * 0.6
    }

    /**
     * Test create details from empty ratios
     */
    public function test_create_details_from_empty_ratios(): void
    {
        // Arrange
        $sale = $this->createMockSale();
        $distributionRatios = [];

        // Act
        $result = $this->saleDetailFactory->createDetailsFromRatios($sale, $distributionRatios);

        // Assert
        $this->assertEmpty($result);
    }

    /**
     * Test insert details success with complete detail structures
     */
    public function test_insert_details_success(): void
    {
        // Arrange - Create complete detail structures with all required fields
        $details = [
            [
                'sale_id' => 1,
                'line_id' => 1,
                'div_id' => 10,
                'brick_id' => 100,
                'quantity' => 50,
                'value' => 500,
                'bonus' => 25,
                'date' => '2023-01-01'
            ],
            [
                'sale_id' => 1,
                'line_id' => 2,
                'div_id' => 20,
                'brick_id' => 200,
                'quantity' => 30,
                'value' => 300,
                'bonus' => 15,
                'date' => '2023-01-01'
            ]
        ];

        // Mock SaleDetail::insert using alias mock
        $saleDetailMock = Mockery::mock('alias:' . SaleDetail::class);
        $saleDetailMock->shouldReceive('insert')
            ->once()
            ->with($details)
            ->andReturn(true);

        // Act
        $result = $this->saleDetailFactory->insertDetails($details);

        // Assert
        $this->assertTrue($result);
    }

    /**
     * Test insert details with empty array should return false
     *
     * Enhanced SaleDetailFactory now properly validates empty details
     * and returns false to prevent orphaned sales without details.
     */
    public function test_insert_details_with_empty_array(): void
    {
        // Arrange
        $details = [];

        // Act (should not call SaleDetail::insert and should return false)
        $result = $this->saleDetailFactory->insertDetails($details);

        // Assert - Should return false for empty details to prevent orphaned sales
        $this->assertFalse($result);
    }

    /**
     * Test insert details failure with complete detail structures
     *
     * This test verifies that when details pass validation but database
     * insertion fails, the method properly returns false.
     */
    public function test_insert_details_failure(): void
    {
        // Arrange - Create complete detail structures that pass validation
        $details = [
            [
                'sale_id' => 1,
                'line_id' => 1,
                'div_id' => 10,
                'brick_id' => 100,
                'quantity' => 50,
                'value' => 500,
                'bonus' => 25,
                'date' => '2023-01-01'
            ]
        ];

        // Mock SaleDetail::insert to return false using alias mock
        $saleDetailMock = Mockery::mock('alias:' . SaleDetail::class);
        $saleDetailMock->shouldReceive('insert')
            ->once()
            ->with($details)
            ->andReturn(false);

        // Act
        $result = $this->saleDetailFactory->insertDetails($details);

        // Assert
        $this->assertFalse($result);
    }

    /**
     * Test create details from ratios with zero percentages
     */
    public function test_create_details_from_ratios_with_zero_percentages(): void
    {
        // Arrange
        $sale = $this->createMockSale();
        $distributionRatios = [
            (object) [
                'line_id' => 1,
                'div_id' => 10,
                'brick_id' => 100,
                'percentage' => 0.0
            ]
        ];

        // Act
        $result = $this->saleDetailFactory->createDetailsFromRatios($sale, $distributionRatios);

        // Assert
        $this->assertCount(1, $result);
        $this->assertEquals(0.0, $result[0]['quantity']);
        $this->assertEquals(0.0, $result[0]['bonus']);
        $this->assertEquals(0.0, $result[0]['value']);
        $this->assertEquals(0.0, $result[0]['ratio']);
    }

    /**
     * Test create details from ratios with array format (after toArray() conversion)
     */
    public function test_create_details_from_ratios_with_array_format(): void
    {
        // Arrange
        $sale = $this->createMockSale();
        $distributionRatios = [
            [
                'line_id' => 1,
                'div_id' => 10,
                'brick_id' => 100,
                'percentage' => 0.6
            ],
            [
                'line_id' => 2,
                'div_id' => 20,
                'brick_id' => 200,
                'percentage' => 0.4
            ]
        ];

        // Act
        $result = $this->saleDetailFactory->createDetailsFromRatios($sale, $distributionRatios);

        // Assert
        $this->assertCount(2, $result);

        // First ratio
        $this->assertEquals(1, $result[0]['sale_id']);
        $this->assertEquals(1, $result[0]['line_id']);
        $this->assertEquals(10, $result[0]['div_id']);
        $this->assertEquals(100, $result[0]['brick_id']);
        $this->assertEquals(60.0, $result[0]['quantity']); // 100 * 0.6
        $this->assertEquals(30.0, $result[0]['bonus']);    // 50 * 0.6
        $this->assertEquals(600.0, $result[0]['value']);   // 1000 * 0.6
        $this->assertEquals(0.6, $result[0]['ratio']);

        // Second ratio
        $this->assertEquals(1, $result[1]['sale_id']);
        $this->assertEquals(2, $result[1]['line_id']);
        $this->assertEquals(20, $result[1]['div_id']);
        $this->assertEquals(200, $result[1]['brick_id']);
        $this->assertEquals(40.0, $result[1]['quantity']); // 100 * 0.4
        $this->assertEquals(20.0, $result[1]['bonus']);    // 50 * 0.4
        $this->assertEquals(400.0, $result[1]['value']);   // 1000 * 0.4
        $this->assertEquals(0.4, $result[1]['ratio']);
    }

    /**
     * Test create details from ratios simulating the exact scenario from SimpleDistributionAlgorithm
     * This reproduces the original issue where Collection->toArray() converts objects to arrays
     */
    public function test_create_details_from_ratios_simulating_simple_distribution_algorithm(): void
    {
        // Arrange
        $sale = $this->createMockSale();

        // Simulate what SalesService::getRatiosForDistribution returns (Collection of objects)
        $distributionRatios = collect([
            (object) [
                'line_id' => 1,
                'div_id' => 10,
                'brick_id' => 100,
                'percentage' => 0.7
            ],
            (object) [
                'line_id' => 2,
                'div_id' => 20,
                'brick_id' => 200,
                'percentage' => 0.3
            ]
        ]);

        // Act - Simulate what SimpleDistributionAlgorithm does: call toArray()
        $result = $this->saleDetailFactory->createDetailsFromRatios($sale, $distributionRatios->toArray());

        // Assert - This should work without the "Attempt to read property on array" error
        $this->assertCount(2, $result);

        // First ratio
        $this->assertEquals(1, $result[0]['sale_id']);
        $this->assertEquals(1, $result[0]['line_id']);
        $this->assertEquals(10, $result[0]['div_id']);
        $this->assertEquals(100, $result[0]['brick_id']);
        $this->assertEquals(70.0, $result[0]['quantity']); // 100 * 0.7
        $this->assertEquals(35.0, $result[0]['bonus']);    // 50 * 0.7
        $this->assertEquals(700.0, $result[0]['value']);   // 1000 * 0.7
        $this->assertEquals(0.7, $result[0]['ratio']);

        // Second ratio
        $this->assertEquals(1, $result[1]['sale_id']);
        $this->assertEquals(2, $result[1]['line_id']);
        $this->assertEquals(20, $result[1]['div_id']);
        $this->assertEquals(200, $result[1]['brick_id']);
        $this->assertEquals(30.0, $result[1]['quantity']); // 100 * 0.3
        $this->assertEquals(15.0, $result[1]['bonus']);    // 50 * 0.3
        $this->assertEquals(300.0, $result[1]['value']);   // 1000 * 0.3
        $this->assertEquals(0.3, $result[1]['ratio']);
    }

    /**
     * Create a mock Sale object for testing
     */
    private function createMockSale(): Sale
    {
        $sale = Mockery::mock(Sale::class);
        $sale->shouldReceive('setAttribute')->andReturnSelf();
        $sale->shouldReceive('getAttribute')->andReturnUsing(function ($key) {
            return match ($key) {
                'id' => 1,
                'quantity' => 100,
                'bonus' => 50,
                'value' => 1000,
                'date'=>'2023-01-01',
                'file_id'=>'test_file',
                default => null,
            };
        });
        $sale->id = 1;
        $sale->date = '2023-01-01';
        $sale->quantity = 100;
        $sale->bonus = 50;
        $sale->value = 1000;
        $sale->file_id = 'test_file';

        return $sale;
    }

    /**
     * Create a mock original Sale with details for testing
     */
    private function createMockOriginalSale(): Sale
    {
        // Mock sale details
        $details = collect([
            (object) [
                'line_id' => 1,
                'div_id' => 10,
                'brick_id' => 100,
                'quantity' => 60,
                'file_id' => 'file1'
            ],
            (object) [
                'line_id' => 2,
                'div_id' => 20,
                'brick_id' => 200,
                'quantity' => 40,
                'file_id' => 'file2'
            ]
        ]);

        // Use partial mock to allow property access
        $sale = Mockery::mock(Sale::class)->makePartial();
        $sale->shouldReceive('setAttribute')->andReturnSelf();
        $sale->shouldReceive('getAttribute')->andReturnUsing(function ($key) use ($details) {
            return match ($key) {
                'id' => 1,
                'quantity' => 100,
                'details' => $details,
                default => null,
            };
        });

        // Set properties directly
        $sale->id = 1;
        $sale->quantity = 100;
        $sale->details = $details;

        return $sale;
    }

    /**
     * Create a mock original Sale with zero quantity for testing
     */
    private function createMockOriginalSaleWithZeroQuantity(): Sale
    {
        // Mock sale details
        $details = collect([
            (object) [
                'line_id' => 1,
                'div_id' => 10,
                'brick_id' => 100,
                'quantity' => 60,
                'file_id' => 'file1'
            ],
            (object) [
                'line_id' => 2,
                'div_id' => 20,
                'brick_id' => 200,
                'quantity' => 40,
                'file_id' => 'file2'
            ]
        ]);

        // Use partial mock to allow property access
        $sale = Mockery::mock(Sale::class)->makePartial();
        $sale->shouldReceive('setAttribute')->andReturnSelf();
        $sale->shouldReceive('getAttribute')->andReturnUsing(function ($key) use ($details) {
            return match ($key) {
                'id' => 1,
                'quantity' => 0, // Zero quantity for this test
                'details' => $details,
                default => null,
            };
        });

        // Set properties directly
        $sale->id = 1;
        $sale->quantity = 0; // Zero quantity
        $sale->details = $details;

        return $sale;
    }

    /**
     * Create a mock limited Sale for testing
     */
    private function createMockLimitedSale(): Sale
    {
        $sale = Mockery::mock(Sale::class);
        $sale->shouldReceive('setAttribute')->andReturnSelf();
        $sale->shouldReceive('getAttribute')->andReturnUsing(function ($key) {
            return match ($key) {
                'id' => 2,
                'quantity' => 50,
                'bonus' => 25,
                'value' => 500,
                default => null,
            };
        });


        $sale->id = 2;
        $sale->quantity = 50;
        $sale->bonus = 25;
        $sale->value = 500;

        return $sale;
    }
}
