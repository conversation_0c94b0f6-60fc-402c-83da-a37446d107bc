<?php

namespace Tests\Unit\Providers;

use App\Providers\DistributionStrategyServiceProvider;
use App\Services\Enums\SaleDistribution;
use App\Services\Sales\Ceiling\Strategies\Distribution\Algorithms\HierarchicalChainDistributionAlgorithm;
use App\Services\Sales\Ceiling\Strategies\Distribution\Algorithms\SimpleDistributionAlgorithm;
use App\Services\Sales\Ceiling\Strategies\Distribution\Algorithms\SplitDistributionAlgorithm;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\LimitCalculatorInterface;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\SaleRepositoryInterface;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\SalesServiceFactoryInterface;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\SettingsProviderInterface;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\TransactionManagerInterface;
use App\Services\Sales\Ceiling\Strategies\Distribution\DistributionStrategyFactory;
use App\Services\Sales\Ceiling\Strategies\Distribution\LocalChainStrategy;
use App\Services\Sales\Ceiling\Strategies\Distribution\PrivatePharmacyStrategy;
use App\Services\Sales\Ceiling\Strategies\Distribution\Services\LimitCalculator;
use App\Services\Sales\Ceiling\Strategies\Distribution\Services\SaleCreator;
use App\Services\Sales\Ceiling\Strategies\Distribution\Services\SaleDetailFactory;
use App\Services\Sales\Ceiling\Strategies\Distribution\Services\SaleRepository;
use App\Services\Sales\Ceiling\Strategies\Distribution\Services\SalesServiceFactory;
use App\Services\Sales\Ceiling\Strategies\Distribution\Services\SalesSettingsProvider;
use App\Services\Sales\Ceiling\Strategies\Distribution\Services\TransactionManager;
use App\Services\Sales\Ceiling\Strategies\Distribution\StoreStrategy;
use App\Services\SalesService;
use App\Services\Structure\Repositories\LineDivisionRepository;
use App\Services\Structure\Repositories\LineDivisionRepositoryInterface;
use Tests\TestCase;

/**
 * Test class for DistributionStrategyServiceProvider
 *
 * Tests the service provider registration and dependency injection
 * to ensure proper Laravel Octane compatibility and SOLID principles.
 *
 * @covers \App\Providers\DistributionStrategyServiceProvider
 */
class DistributionStrategyServiceProviderTest extends TestCase
{
    private DistributionStrategyServiceProvider $provider;

    protected function setUp(): void
    {
        parent::setUp();
        $this->provider = new DistributionStrategyServiceProvider($this->app);
    }

    /**
     * Test that core interfaces are properly registered
     */
    public function test_core_interfaces_registration(): void
    {
        // Arrange & Act
        $this->provider->register();

        // Assert - Core interfaces should be bound to their implementations
        $this->assertTrue($this->app->bound(TransactionManagerInterface::class));
        $this->assertTrue($this->app->bound(SettingsProviderInterface::class));
        $this->assertTrue($this->app->bound(LimitCalculatorInterface::class));
        $this->assertTrue($this->app->bound(SaleRepositoryInterface::class));
        $this->assertTrue($this->app->bound(LineDivisionRepositoryInterface::class));
        $this->assertTrue($this->app->bound(SalesServiceFactoryInterface::class));

        // Verify implementations
        $this->assertInstanceOf(TransactionManager::class, $this->app->make(TransactionManagerInterface::class));
        $this->assertInstanceOf(SalesSettingsProvider::class, $this->app->make(SettingsProviderInterface::class));
        $this->assertInstanceOf(LimitCalculator::class, $this->app->make(LimitCalculatorInterface::class));
        $this->assertInstanceOf(SaleRepository::class, $this->app->make(SaleRepositoryInterface::class));
        $this->assertInstanceOf(LineDivisionRepository::class, $this->app->make(LineDivisionRepositoryInterface::class));
        $this->assertInstanceOf(SalesServiceFactory::class, $this->app->make(SalesServiceFactoryInterface::class));
    }

    /**
     * Test that service factories are properly registered
     */
    public function test_service_factories_registration(): void
    {
        // Arrange & Act
        $this->provider->register();

        // Assert - Singleton services should be registered as singletons
        $this->assertTrue($this->app->bound(SaleDetailFactory::class));
        $this->assertTrue($this->app->bound(SaleCreator::class));
        $this->assertTrue($this->app->bound(DistributionStrategyFactory::class));

        // Verify singleton behavior
        $factory1 = $this->app->make(SaleDetailFactory::class);
        $factory2 = $this->app->make(SaleDetailFactory::class);
        $this->assertSame($factory1, $factory2);

        // SalesService instances should NOT be singletons
        $this->assertTrue($this->app->bound('SalesService.Normal'));
        $this->assertTrue($this->app->bound('SalesService.Direct'));

        $service1 = $this->app->make('SalesService.Normal');
        $service2 = $this->app->make('SalesService.Normal');
        $this->assertNotSame($service1, $service2);
        $this->assertInstanceOf(SalesService::class, $service1);
        $this->assertInstanceOf(SalesService::class, $service2);
    }

    /**
     * Test that distribution algorithms are properly registered
     */
    public function test_distribution_algorithms_registration(): void
    {
        // Arrange & Act
        $this->provider->register();

        // Assert
        $this->assertTrue($this->app->bound(SimpleDistributionAlgorithm::class));
        $this->assertTrue($this->app->bound(SplitDistributionAlgorithm::class));
        $this->assertTrue($this->app->bound(HierarchicalChainDistributionAlgorithm::class));

        // Verify instances can be created
        $simpleAlgorithm = $this->app->make(SimpleDistributionAlgorithm::class);
        $splitAlgorithm = $this->app->make(SplitDistributionAlgorithm::class);
        $hierarchicalAlgorithm = $this->app->make(HierarchicalChainDistributionAlgorithm::class);

        $this->assertInstanceOf(SimpleDistributionAlgorithm::class, $simpleAlgorithm);
        $this->assertInstanceOf(SplitDistributionAlgorithm::class, $splitAlgorithm);
        $this->assertInstanceOf(HierarchicalChainDistributionAlgorithm::class, $hierarchicalAlgorithm);

        // Each resolution should create fresh instances (not singletons)
        $this->assertNotSame($simpleAlgorithm, $this->app->make(SimpleDistributionAlgorithm::class));
        $this->assertNotSame($splitAlgorithm, $this->app->make(SplitDistributionAlgorithm::class));
        $this->assertNotSame($hierarchicalAlgorithm, $this->app->make(HierarchicalChainDistributionAlgorithm::class));
    }

    /**
     * Test that distribution strategies are properly registered
     */
    public function test_distribution_strategies_registration(): void
    {
        // Arrange & Act
        $this->provider->register();

        // Assert
        $this->assertTrue($this->app->bound(PrivatePharmacyStrategy::class));
        $this->assertTrue($this->app->bound(StoreStrategy::class));
        $this->assertTrue($this->app->bound(LocalChainStrategy::class));

        // Verify instances can be created
        $privatePharmacyStrategy = $this->app->make(PrivatePharmacyStrategy::class);
        $storeStrategy = $this->app->make(StoreStrategy::class);
        $localChainStrategy = $this->app->make(LocalChainStrategy::class);

        $this->assertInstanceOf(PrivatePharmacyStrategy::class, $privatePharmacyStrategy);
        $this->assertInstanceOf(StoreStrategy::class, $storeStrategy);
        $this->assertInstanceOf(LocalChainStrategy::class, $localChainStrategy);

        // Each resolution should create fresh instances (not singletons)
        $this->assertNotSame($privatePharmacyStrategy, $this->app->make(PrivatePharmacyStrategy::class));
        $this->assertNotSame($storeStrategy, $this->app->make(StoreStrategy::class));
        $this->assertNotSame($localChainStrategy, $this->app->make(LocalChainStrategy::class));
    }

    /**
     * Test SalesService factory integration
     */
    public function test_sales_service_factory_integration(): void
    {
        // Arrange & Act
        $this->provider->register();

        // Assert - SalesService instances should be created via factory
        $normalService = $this->app->make('SalesService.Normal');
        $directService = $this->app->make('SalesService.Direct');

        $this->assertInstanceOf(SalesService::class, $normalService);
        $this->assertInstanceOf(SalesService::class, $directService);

        // Verify they have different configurations
        $normalReflection = new \ReflectionClass($normalService);
        $directReflection = new \ReflectionClass($directService);

        $normalDistributionProperty = $normalReflection->getProperty('saleDistribution');
        $normalDistributionProperty->setAccessible(true);
        $this->assertEquals(SaleDistribution::NORMAL, $normalDistributionProperty->getValue($normalService));

        $directDistributionProperty = $directReflection->getProperty('saleDistribution');
        $directDistributionProperty->setAccessible(true);
        $this->assertEquals(SaleDistribution::DIRECT, $directDistributionProperty->getValue($directService));
    }

    /**
     * Test Octane compatibility - fresh instances
     */
    public function test_octane_compatibility_fresh_instances(): void
    {
        // Arrange & Act
        $this->provider->register();

        // Create multiple instances to simulate multiple requests
        $instances = [];
        for ($i = 0; $i < 10; $i++) {
            $instances[] = [
                'normal' => $this->app->make('SalesService.Normal'),
                'direct' => $this->app->make('SalesService.Direct'),
                'private_pharmacy' => $this->app->make(PrivatePharmacyStrategy::class),
                'store' => $this->app->make(StoreStrategy::class),
                'local_chain' => $this->app->make(LocalChainStrategy::class),
            ];
        }

        // Assert - All instances should be unique (fresh instances for each request)
        for ($i = 0; $i < 9; $i++) {
            for ($j = $i + 1; $j < 10; $j++) {
                $this->assertNotSame($instances[$i]['normal'], $instances[$j]['normal']);
                $this->assertNotSame($instances[$i]['direct'], $instances[$j]['direct']);
                $this->assertNotSame($instances[$i]['private_pharmacy'], $instances[$j]['private_pharmacy']);
                $this->assertNotSame($instances[$i]['store'], $instances[$j]['store']);
                $this->assertNotSame($instances[$i]['local_chain'], $instances[$j]['local_chain']);
            }
        }
    }

    /**
     * Test that singletons are properly shared
     */
    public function test_singleton_services_are_shared(): void
    {
        // Arrange & Act
        $this->provider->register();

        // Assert - Singleton services should return the same instance
        $factory1 = $this->app->make(SalesServiceFactoryInterface::class);
        $factory2 = $this->app->make(SalesServiceFactoryInterface::class);
        $this->assertSame($factory1, $factory2);

        $detailFactory1 = $this->app->make(SaleDetailFactory::class);
        $detailFactory2 = $this->app->make(SaleDetailFactory::class);
        $this->assertSame($detailFactory1, $detailFactory2);

        $creator1 = $this->app->make(SaleCreator::class);
        $creator2 = $this->app->make(SaleCreator::class);
        $this->assertSame($creator1, $creator2);

        $strategyFactory1 = $this->app->make(DistributionStrategyFactory::class);
        $strategyFactory2 = $this->app->make(DistributionStrategyFactory::class);
        $this->assertSame($strategyFactory1, $strategyFactory2);
    }

    /**
     * Test dependency injection chain works correctly
     */
    public function test_dependency_injection_chain(): void
    {
        // Arrange & Act
        $this->provider->register();

        // Assert - Complex dependency chains should resolve correctly
        $privatePharmacyStrategy = $this->app->make(PrivatePharmacyStrategy::class);
        
        // Use reflection to verify dependencies are properly injected
        $reflection = new \ReflectionClass($privatePharmacyStrategy);
        
        $salesServiceProperty = $reflection->getProperty('salesService');
        $salesServiceProperty->setAccessible(true);
        $salesService = $salesServiceProperty->getValue($privatePharmacyStrategy);
        
        $this->assertInstanceOf(SalesService::class, $salesService);
        
        // Verify the SalesService has the correct configuration
        $salesServiceReflection = new \ReflectionClass($salesService);
        $distributionTypeProperty = $salesServiceReflection->getProperty('distributionType');
        $distributionTypeProperty->setAccessible(true);
        
        // Should be configured for PRIVATE_PHARMACY distribution type
        $this->assertNotNull($distributionTypeProperty->getValue($salesService));
    }

    /**
     * Test memory usage with repeated service resolution
     */
    public function test_memory_usage_with_repeated_resolution(): void
    {
        // Arrange
        $this->provider->register();
        $initialMemory = memory_get_usage(true);

        // Act - Resolve services many times
        for ($i = 0; $i < 100; $i++) {
            $service = $this->app->make('SalesService.Normal');
            $strategy = $this->app->make(PrivatePharmacyStrategy::class);
            
            // Explicitly unset to help garbage collection
            unset($service, $strategy);
            
            if ($i % 20 === 0) {
                gc_collect_cycles();
            }
        }

        gc_collect_cycles();
        $finalMemory = memory_get_usage(true);
        $memoryIncrease = $finalMemory - $initialMemory;

        // Assert - Memory increase should be reasonable (less than 10MB for 100 resolutions)
        $this->assertLessThan(10 * 1024 * 1024, $memoryIncrease,
            "Memory usage increased by {$memoryIncrease} bytes, which may indicate memory leaks in service resolution");
    }
}
