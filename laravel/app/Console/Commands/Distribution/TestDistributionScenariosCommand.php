<?php

namespace App\Console\Commands\Distribution;

use App\Sale;
use App\Services\Enums\SaleDistribution;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\SalesServiceFactoryInterface;
use App\Services\Sales\Ceiling\Strategies\Distribution\DistributionType;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

/**
 * Command to test various distribution scenarios to identify
 * the exact conditions that cause getRatiosForDistribution to return empty results
 */
class TestDistributionScenariosCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'distribution:test-scenarios 
                            {--clear-cache : Clear cache before testing}
                            {--product-id=41 : Product ID to test}
                            {--date=2025-05-01 : Date to test}
                            {--distributors=1,2,3,4,5 : Comma-separated distributor IDs}';

    /**
     * The console command description.
     */
    protected $description = 'Test various distribution scenarios to identify conditions causing empty results';

    private SalesServiceFactoryInterface $salesServiceFactory;

    public function __construct(SalesServiceFactoryInterface $salesServiceFactory)
    {
        parent::__construct();
        $this->salesServiceFactory = $salesServiceFactory;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        if ($this->option('clear-cache')) {
            Cache::flush();
            $this->info('Cache cleared.');
        }

        $productId = (int) $this->option('product-id');
        $date = $this->option('date');
        $distributorIds = array_map('intval', explode(',', $this->option('distributors')));

        $this->info("=== DISTRIBUTION SCENARIOS TEST ===");
        $this->info("Product ID: {$productId}");
        $this->info("Date: {$date}");
        $this->info("Distributor IDs: " . implode(', ', $distributorIds));
        $this->newLine();

        // Test 1: Basic data availability
        $this->testDataAvailability($productId, $date, $distributorIds);
        $this->newLine();

        // Test 2: Different distribution types
        $this->testDistributionTypes($productId, $date, $distributorIds);
        $this->newLine();

        // Test 3: Different ceiling states
        $this->testCeilingStates($productId, $date, $distributorIds);
        $this->newLine();

        // Test 4: Division filter scenarios
        $this->testDivisionFilters($productId, $date, $distributorIds);
        $this->newLine();

        // Test 5: Transaction scenarios
        $this->testTransactionScenarios($productId, $date, $distributorIds);
        $this->newLine();

        // Test 6: Cache scenarios
        $this->testCacheScenarios($productId, $date, $distributorIds);

        return 0;
    }

    private function testDataAvailability(int $productId, string $date, array $distributorIds): void
    {
        $this->info("1. DATA AVAILABILITY TEST");

        $salesCount = Sale::where('product_id', $productId)
            ->whereDate('date', $date)
            ->whereIn('distributor_id', $distributorIds)
            ->count();

        $salesWithDetails = Sale::where('product_id', $productId)
            ->whereDate('date', $date)
            ->whereIn('distributor_id', $distributorIds)
            ->whereHas('details')
            ->count();

        $ceilingDistribution = Sale::where('product_id', $productId)
            ->whereDate('date', $date)
            ->whereIn('distributor_id', $distributorIds)
            ->selectRaw('ceiling, COUNT(*) as count')
            ->groupBy('ceiling')
            ->get();

        $this->table(
            ['Metric', 'Count'],
            [
                ['Total Sales', $salesCount],
                ['Sales with Details', $salesWithDetails],
                ['Sales without Details', $salesCount - $salesWithDetails]
            ]
        );

        $this->info("Ceiling Distribution:");
        foreach ($ceilingDistribution as $ceiling) {
            $ceilingName = match($ceiling->ceiling) {
                0 => 'BELOW',
                1 => 'ABOVE',
                2 => 'DISTRIBUTED',
                default => 'UNKNOWN'
            };
            $this->line("  {$ceilingName}: {$ceiling->count}");
        }
    }

    private function testDistributionTypes(int $productId, string $date, array $distributorIds): void
    {
        $this->info("2. DISTRIBUTION TYPES TEST");

        $distributionTypes = [
            'No Type' => null,
            'STORES' => DistributionType::STORES,
            'PRIVATE_PHARMACY' => DistributionType::PRIVATE_PHARMACY,
            'LOCAL_CHAINS' => DistributionType::LOCAL_CHAINS,
        ];

        $results = [];

        foreach ($distributionTypes as $typeName => $type) {
            $service = $this->salesServiceFactory->createForDistribution(SaleDistribution::NORMAL, $type);
            $ratios = $service->getRatiosForDistribution($date, $productId, $distributorIds);
            
            $results[] = [
                $typeName,
                $ratios->count(),
                $ratios->sum('percentage'),
                $ratios->count() > 0 ? '✅' : '❌'
            ];
        }

        $this->table(['Distribution Type', 'Results Count', 'Total %', 'Status'], $results);
    }

    private function testCeilingStates(int $productId, string $date, array $distributorIds): void
    {
        $this->info("3. CEILING STATES TEST");

        // Test with different ceiling filters
        $ceilingTests = [
            'BELOW (0)' => [0],
            'ABOVE (1)' => [1],
            'DISTRIBUTED (2)' => [2],
            'BELOW + DISTRIBUTED' => [0, 2]
        ];

        $results = [];

        foreach ($ceilingTests as $testName => $ceilings) {
            // Temporarily modify SalesService to test different ceiling filters
            $salesCount = Sale::where('product_id', $productId)
                ->whereDate('date', $date)
                ->whereIn('distributor_id', $distributorIds)
                ->whereIn('ceiling', $ceilings)
                ->count();

            $service = $this->salesServiceFactory->createForDistribution(SaleDistribution::NORMAL, DistributionType::STORES);
            $ratios = $service->getRatiosForDistribution($date, $productId, $distributorIds);

            $results[] = [
                $testName,
                $salesCount,
                $ratios->count(),
                $ratios->count() > 0 ? '✅' : '❌'
            ];
        }

        $this->table(['Ceiling Filter', 'Sales Count', 'Results Count', 'Status'], $results);
    }

    private function testDivisionFilters(int $productId, string $date, array $distributorIds): void
    {
        $this->info("4. DIVISION FILTER TEST");

        $service = $this->salesServiceFactory->createForDistribution(SaleDistribution::NORMAL, DistributionType::STORES);

        $divisionTests = [
            'No Filter' => [],
            'Single Division' => [611],
            'Multiple Divisions' => [608, 609, 610, 611],
            'Large Division Set' => [608,609,610,611,622,635,637,638,643,647,648,655,656,662],
            'Non-existent Division' => [99999]
        ];

        $results = [];

        foreach ($divisionTests as $testName => $divisionIds) {
            $ratios = $service->getRatiosForDistribution($date, $productId, $distributorIds, $divisionIds);
            
            $results[] = [
                $testName,
                count($divisionIds),
                $ratios->count(),
                $ratios->sum('percentage'),
                $ratios->count() > 0 ? '✅' : '❌'
            ];
        }

        $this->table(['Division Filter', 'Division Count', 'Results Count', 'Total %', 'Status'], $results);
    }

    private function testTransactionScenarios(int $productId, string $date, array $distributorIds): void
    {
        $this->info("5. TRANSACTION SCENARIOS TEST");

        $service = $this->salesServiceFactory->createForDistribution(SaleDistribution::NORMAL, DistributionType::STORES);

        // Test outside transaction
        $normalResult = $service->getRatiosForDistribution($date, $productId, $distributorIds);

        // Test inside transaction
        DB::beginTransaction();
        $transactionResult = $service->getRatiosForDistribution($date, $productId, $distributorIds);
        DB::rollBack();

        // Test with read uncommitted isolation
        DB::statement('SET SESSION TRANSACTION ISOLATION LEVEL READ UNCOMMITTED');
        $readUncommittedResult = $service->getRatiosForDistribution($date, $productId, $distributorIds);
        DB::statement('SET SESSION TRANSACTION ISOLATION LEVEL REPEATABLE READ'); // Reset

        $this->table(
            ['Scenario', 'Results Count', 'Status'],
            [
                ['Normal (No Transaction)', $normalResult->count(), $normalResult->count() > 0 ? '✅' : '❌'],
                ['Inside Transaction', $transactionResult->count(), $transactionResult->count() > 0 ? '✅' : '❌'],
                ['Read Uncommitted', $readUncommittedResult->count(), $readUncommittedResult->count() > 0 ? '✅' : '❌']
            ]
        );
    }

    private function testCacheScenarios(int $productId, string $date, array $distributorIds): void
    {
        $this->info("6. CACHE SCENARIOS TEST");

        $service = $this->salesServiceFactory->createForDistribution(SaleDistribution::NORMAL, DistributionType::STORES);

        // Test 1: Fresh cache
        Cache::flush();
        $freshCacheResult = $service->getRatiosForDistribution($date, $productId, $distributorIds);

        // Test 2: Cached result
        $cachedResult = $service->getRatiosForDistribution($date, $productId, $distributorIds);

        // Test 3: New service instance (different cache key)
        $newService = $this->salesServiceFactory->createForDistribution(SaleDistribution::NORMAL, DistributionType::STORES);
        $newInstanceResult = $newService->getRatiosForDistribution($date, $productId, $distributorIds);

        $this->table(
            ['Cache Scenario', 'Results Count', 'Status'],
            [
                ['Fresh Cache', $freshCacheResult->count(), $freshCacheResult->count() > 0 ? '✅' : '❌'],
                ['Cached Result', $cachedResult->count(), $cachedResult->count() > 0 ? '✅' : '❌'],
                ['New Instance', $newInstanceResult->count(), $newInstanceResult->count() > 0 ? '✅' : '❌']
            ]
        );

        // Check if all results are identical
        $allIdentical = $freshCacheResult->count() === $cachedResult->count() && 
                       $cachedResult->count() === $newInstanceResult->count();

        if ($allIdentical) {
            $this->info("✅ All cache scenarios return consistent results");
        } else {
            $this->error("❌ Cache scenarios return inconsistent results!");
        }
    }
}
