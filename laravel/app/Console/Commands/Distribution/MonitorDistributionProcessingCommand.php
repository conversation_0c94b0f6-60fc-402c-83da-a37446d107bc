<?php

namespace App\Console\Commands\Distribution;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\File;

/**
 * Command to monitor distribution processing in real-time
 * and capture the exact context when getRatiosForDistribution returns empty results
 */
class MonitorDistributionProcessingCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'distribution:monitor 
                            {--tail : Tail the log file in real-time}
                            {--filter=STORES_DISTRIBUTION_DEBUG : Filter log entries by context}
                            {--since=5min : Show logs since this time (e.g., 5min, 1hour, 1day)}';

    /**
     * The console command description.
     */
    protected $description = 'Monitor distribution processing to identify when getRatiosForDistribution returns empty results';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info("=== DISTRIBUTION PROCESSING MONITOR ===");
        $this->info("Monitoring for STORES distribution issues...");
        $this->newLine();

        $logFile = storage_path('logs/laravel-' . now()->format('Y-m-d') . '.log');
        
        if (!File::exists($logFile)) {
            $this->error("Log file not found: {$logFile}");
            return 1;
        }

        if ($this->option('tail')) {
            $this->tailLogFile($logFile);
        } else {
            $this->analyzeLogFile($logFile);
        }

        return 0;
    }

    private function tailLogFile(string $logFile): void
    {
        $this->info("Tailing log file: {$logFile}");
        $this->info("Press Ctrl+C to stop...");
        $this->newLine();

        $filter = $this->option('filter');
        $handle = fopen($logFile, 'r');
        
        if (!$handle) {
            $this->error("Cannot open log file for reading");
            return;
        }

        // Go to end of file
        fseek($handle, 0, SEEK_END);

        while (true) {
            $line = fgets($handle);
            
            if ($line === false) {
                usleep(100000); // Sleep for 0.1 seconds
                continue;
            }

            if ($filter && strpos($line, $filter) === false) {
                continue;
            }

            // Parse and display relevant log entries
            $this->displayLogEntry($line);
        }

        fclose($handle);
    }

    private function analyzeLogFile(string $logFile): void
    {
        $this->info("Analyzing log file: {$logFile}");
        
        $filter = $this->option('filter');
        $since = $this->option('since');
        $sinceTime = $this->parseSinceTime($since);

        $lines = file($logFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        $relevantEntries = [];
        $emptyResultsCount = 0;
        $successfulResultsCount = 0;

        foreach ($lines as $line) {
            if ($filter && strpos($line, $filter) === false) {
                continue;
            }

            // Parse timestamp from log line
            if (preg_match('/\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\]/', $line, $matches)) {
                $logTime = \Carbon\Carbon::parse($matches[1]);
                
                if ($sinceTime && $logTime->lt($sinceTime)) {
                    continue;
                }
            }

            $relevantEntries[] = $line;

            // Count empty vs successful results
            if (strpos($line, 'ratios_count":0') !== false || strpos($line, 'No distribution ratios found') !== false) {
                $emptyResultsCount++;
            } elseif (strpos($line, 'ratios_count":') !== false && strpos($line, 'ratios_count":0') === false) {
                $successfulResultsCount++;
            }
        }

        $this->displayAnalysisResults($relevantEntries, $emptyResultsCount, $successfulResultsCount);
    }

    private function parseSinceTime(string $since): ?\Carbon\Carbon
    {
        if (!$since) {
            return null;
        }

        $now = now();
        
        if (preg_match('/(\d+)(min|hour|day)s?/', $since, $matches)) {
            $amount = (int) $matches[1];
            $unit = $matches[2];
            
            return match($unit) {
                'min' => $now->subMinutes($amount),
                'hour' => $now->subHours($amount),
                'day' => $now->subDays($amount),
                default => null
            };
        }

        return null;
    }

    private function displayLogEntry(string $line): void
    {
        // Extract key information from log line
        if (strpos($line, 'Getting ratios for distribution') !== false) {
            $this->line("🔍 " . $this->extractLogInfo($line, 'Getting ratios for distribution'));
        } elseif (strpos($line, 'Distribution ratios retrieved') !== false) {
            $ratiosCount = $this->extractJsonValue($line, 'ratios_count');
            $status = $ratiosCount > 0 ? '✅' : '❌';
            $this->line("{$status} Results: {$ratiosCount} ratios");
        } elseif (strpos($line, 'No distribution ratios found') !== false) {
            $this->error("❌ EMPTY RESULTS DETECTED!");
            $this->line("   " . $this->extractLogInfo($line, 'No distribution ratios found'));
        } elseif (strpos($line, 'Query execution failed') !== false) {
            $this->error("💥 QUERY FAILED!");
            $this->line("   " . $this->extractLogInfo($line, 'Query execution failed'));
        }
    }

    private function displayAnalysisResults(array $entries, int $emptyCount, int $successCount): void
    {
        $this->info("=== ANALYSIS RESULTS ===");
        $this->table(
            ['Metric', 'Count'],
            [
                ['Total Log Entries', count($entries)],
                ['Empty Results', $emptyCount],
                ['Successful Results', $successCount],
                ['Success Rate', $successCount + $emptyCount > 0 ? round(($successCount / ($successCount + $emptyCount)) * 100, 2) . '%' : 'N/A']
            ]
        );

        if ($emptyCount > 0) {
            $this->newLine();
            $this->error("⚠️  Found {$emptyCount} instances of empty results!");
            $this->info("Recent empty result entries:");
            
            $emptyEntries = array_filter($entries, function($line) {
                return strpos($line, 'ratios_count":0') !== false || strpos($line, 'No distribution ratios found') !== false;
            });

            foreach (array_slice($emptyEntries, -3) as $entry) {
                $this->line("  " . $this->extractLogInfo($entry, 'empty result'));
            }
        }

        if ($successCount > 0) {
            $this->newLine();
            $this->info("✅ Found {$successCount} successful distribution queries");
        }
    }

    private function extractLogInfo(string $line, string $context): string
    {
        // Extract timestamp
        $timestamp = '';
        if (preg_match('/\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\]/', $line, $matches)) {
            $timestamp = $matches[1];
        }

        // Extract key parameters
        $productId = $this->extractJsonValue($line, 'product_id');
        $distributorIds = $this->extractJsonValue($line, 'distributor_ids');
        $divisionIds = $this->extractJsonValue($line, 'division_ids');
        $distributionType = $this->extractJsonValue($line, 'distribution_type');

        return sprintf(
            "[%s] Product:%s, Distributors:%s, Divisions:%s, Type:%s",
            $timestamp,
            $productId ?: 'N/A',
            is_array($distributorIds) ? implode(',', $distributorIds) : ($distributorIds ?: 'N/A'),
            is_array($divisionIds) ? implode(',', $divisionIds) : ($divisionIds ?: 'N/A'),
            $distributionType ?: 'N/A'
        );
    }

    private function extractJsonValue(string $line, string $key): mixed
    {
        if (preg_match('/"' . preg_quote($key) . '":([^,}]+)/', $line, $matches)) {
            $value = trim($matches[1], '"');
            
            // Try to decode as JSON if it looks like an array
            if (str_starts_with($value, '[') && str_ends_with($value, ']')) {
                return json_decode($value, true);
            }
            
            return is_numeric($value) ? (int) $value : $value;
        }
        
        return null;
    }
}
