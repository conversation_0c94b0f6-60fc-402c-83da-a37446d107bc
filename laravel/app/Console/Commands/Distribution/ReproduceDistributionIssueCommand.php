<?php

namespace App\Console\Commands\Distribution;

use App\Sale;
use App\Services\Enums\SaleDistribution;
use App\Services\Sales\Ceiling\Strategies\Distribution\Algorithms\SplitDistributionAlgorithm;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\SalesServiceFactoryInterface;
use App\Services\Sales\Ceiling\Strategies\Distribution\DistributionType;
use App\Services\SalesService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

/**
 * Command to reproduce the exact distribution processing issue
 * where getRatiosForDistribution returns empty results during actual processing
 */
class ReproduceDistributionIssueCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'distribution:reproduce-issue 
                            {--clear-cache : Clear cache before testing}
                            {--with-transaction : Test within database transaction}';

    /**
     * The console command description.
     */
    protected $description = 'Reproduce the exact distribution processing issue where getRatiosForDistribution returns empty results';

    private SalesServiceFactoryInterface $salesServiceFactory;
    private SplitDistributionAlgorithm $splitAlgorithm;

    public function __construct(
        SalesServiceFactoryInterface $salesServiceFactory,
        SplitDistributionAlgorithm $splitAlgorithm
    ) {
        parent::__construct();
        $this->salesServiceFactory = $salesServiceFactory;
        $this->splitAlgorithm = $splitAlgorithm;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        // Disable logging to avoid permission issues
        config(['logging.default' => 'null']);

        // Also set the environment to testing to reduce logging
        app()->detectEnvironment(function () {
            return 'testing';
        });

        if ($this->option('clear-cache')) {
            Cache::flush();
            $this->info('Cache cleared.');
        }

        $this->info("=== REPRODUCING DISTRIBUTION PROCESSING ISSUE ===");
        $this->newLine();

        // Test parameters from the original issue
        $date = "2025-05-01";
        $productId = 41;
        $distributorIds = [1, 2, 3, 4, 5];

        $this->info("Test Parameters:");
        $this->info("Date: {$date}");
        $this->info("Product ID: {$productId}");
        $this->info("Distributor IDs: " . implode(', ', $distributorIds));
        $this->newLine();

        // Test 1: Manual testing (works)
        $this->info("1. MANUAL TESTING (Expected to work)");
        $this->testManualApproach($date, $productId, $distributorIds);
        $this->newLine();

        // Test 2: Simulate actual distribution processing context
        $this->info("2. SIMULATE ACTUAL DISTRIBUTION PROCESSING");
        $this->simulateActualDistributionProcessing($date, $productId, $distributorIds);
        $this->newLine();

        // Test 3: Test with database transaction (if requested)
        if ($this->option('with-transaction')) {
            $this->info("3. TEST WITHIN DATABASE TRANSACTION");
            $this->testWithinTransaction($date, $productId, $distributorIds);
            $this->newLine();
        }

        // Test 4: Test with different database states
        $this->info("4. TEST DATABASE STATE SENSITIVITY");
        $this->testDatabaseStateSensitivity($date, $productId, $distributorIds);

        return 0;
    }

    private function testManualApproach(string $date, int $productId, array $distributorIds): void
    {
        $this->line("Testing manual approach (like Tinker)...");
        
        $service = app(SalesServiceFactoryInterface::class)->createNormal(DistributionType::STORES);
        
        $result = $service->getRatiosForDistribution($date, $productId, $distributorIds);
        
        $this->table(
            ['Metric', 'Value'],
            [
                ['Results Count', $result->count()],
                ['Total Percentage', $result->sum('percentage')],
                ['Status', $result->count() > 0 ? '✓ SUCCESS' : '✗ FAILED']
            ]
        );
    }

    private function simulateActualDistributionProcessing(string $date, int $productId, array $distributorIds): void
    {
        $this->line("Simulating the exact SplitDistributionAlgorithm flow...");
        
        // Create a mock sale for testing
        $mockSale = new Sale([
            'id' => 999999,
            'date' => $date,
            'product_id' => $productId,
            'distributor_id' => $distributorIds[0],
            'quantity' => 100,
            'value' => 1000,
            'bonus' => 100,
            'ceiling' => 1 // ABOVE (as it would be during distribution processing)
        ]);

        // Create a mock original sale with details
        $mockOriginalSale = new Sale([
            'id' => 999998,
            'date' => $date,
            'product_id' => $productId,
            'distributor_id' => $distributorIds[0],
            'quantity' => 50,
            'value' => 500,
            'bonus' => 50,
            'ceiling' => 0 // BELOW
        ]);

        // Mock the details collection
        $mockOriginalSale->setRelation('details', collect([
            (object) ['div_id' => 611, 'line_id' => 15, 'brick_id' => 237, 'quantity' => 50]
        ]));

        $this->line("--- Testing Primary Distribution (90%) ---");
        
        // Test primary distribution (no division filter)
        $primaryService = $this->salesServiceFactory->createForDistribution(SaleDistribution::NORMAL, DistributionType::STORES);
        $primaryRatios = $primaryService->getRatiosForDistribution($date, $productId, $distributorIds);
        
        $this->table(
            ['Distribution', 'Results Count', 'Total Percentage', 'Status'],
            [
                ['Primary (90%)', $primaryRatios->count(), $primaryRatios->sum('percentage'), $primaryRatios->count() > 0 ? '✓' : '✗']
            ]
        );

        $this->line("--- Testing Secondary Distribution (10%) ---");
        
        // Test secondary distribution (with division filter from original sale)
        $divisionIds = $mockOriginalSale->details->pluck('div_id')->toArray();
        $secondaryService = $this->salesServiceFactory->createForDistribution(SaleDistribution::NORMAL, DistributionType::STORES);
        $secondaryRatios = $secondaryService->getRatiosForDistribution($date, $productId, $distributorIds, $divisionIds);
        
        $this->table(
            ['Distribution', 'Results Count', 'Total Percentage', 'Division Filter', 'Status'],
            [
                ['Secondary (10%)', $secondaryRatios->count(), $secondaryRatios->sum('percentage'), implode(',', $divisionIds), $secondaryRatios->count() > 0 ? '✓' : '✗']
            ]
        );

        // Test the actual SplitDistributionAlgorithm
        $this->line("--- Testing SplitDistributionAlgorithm ---");
        
        try {
            $distributionSuccess = $this->splitAlgorithm->distributeExcessSale(
                $mockSale,
                $distributorIds,
                $mockOriginalSale,
                DistributionType::STORES
            );
            
            $this->table(
                ['Algorithm', 'Success', 'Status'],
                [
                    ['SplitDistributionAlgorithm', $distributionSuccess ? 'Yes' : 'No', $distributionSuccess ? '✓' : '✗']
                ]
            );
        } catch (\Exception $e) {
            $this->error("SplitDistributionAlgorithm failed: " . $e->getMessage());
        }
    }

    private function testWithinTransaction(string $date, int $productId, array $distributorIds): void
    {
        $this->line("Testing within database transaction...");
        
        DB::beginTransaction();
        
        try {
            $service = $this->salesServiceFactory->createForDistribution(SaleDistribution::NORMAL, DistributionType::STORES);
            $result = $service->getRatiosForDistribution($date, $productId, $distributorIds);
            
            $this->table(
                ['Context', 'Results Count', 'Status'],
                [
                    ['Within Transaction', $result->count(), $result->count() > 0 ? '✓' : '✗']
                ]
            );
            
        } finally {
            DB::rollBack();
        }
    }

    private function testDatabaseStateSensitivity(string $date, int $productId, array $distributorIds): void
    {
        $this->line("Testing database state sensitivity...");
        
        // Test 1: Check current sales ceiling states
        $ceilingStates = DB::table('sales')
            ->where('product_id', $productId)
            ->whereYear('date', 2025)
            ->whereMonth('date', 5)
            ->whereIn('distributor_id', $distributorIds)
            ->selectRaw('ceiling, COUNT(*) as count')
            ->groupBy('ceiling')
            ->get();
        
        $this->info("Current ceiling states:");
        foreach ($ceilingStates as $state) {
            $ceilingName = match($state->ceiling) {
                0 => 'BELOW',
                1 => 'ABOVE', 
                2 => 'DISTRIBUTED',
                default => 'UNKNOWN'
            };
            $this->line("  {$ceilingName} ({$state->ceiling}): {$state->count} sales");
        }
        
        // Test 2: Temporarily modify a sale's ceiling and test
        $testSale = DB::table('sales')
            ->where('product_id', $productId)
            ->whereYear('date', 2025)
            ->whereMonth('date', 5)
            ->whereIn('distributor_id', $distributorIds)
            ->where('ceiling', 0)
            ->first();
        
        if ($testSale) {
            $this->line("Testing with modified ceiling state...");
            
            // Temporarily change ceiling to ABOVE
            DB::table('sales')->where('id', $testSale->id)->update(['ceiling' => 1]);
            
            $service = $this->salesServiceFactory->createForDistribution(SaleDistribution::NORMAL, DistributionType::STORES);
            $result = $service->getRatiosForDistribution($date, $productId, $distributorIds);
            
            // Restore original ceiling
            DB::table('sales')->where('id', $testSale->id)->update(['ceiling' => 0]);
            
            $this->table(
                ['Test', 'Results Count', 'Status'],
                [
                    ['With Modified Ceiling', $result->count(), $result->count() > 0 ? '✓' : '✗']
                ]
            );
        }
    }
}
