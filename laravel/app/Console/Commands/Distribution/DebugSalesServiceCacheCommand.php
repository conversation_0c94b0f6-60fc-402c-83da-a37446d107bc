<?php

namespace App\Console\Commands\Distribution;

use App\Services\Enums\SaleDistribution;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\SalesServiceFactoryInterface;
use App\Services\Sales\Ceiling\Strategies\Distribution\DistributionType;
use App\Services\SalesService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * Debug command to investigate SalesService cache key and instance differences
 * between manual testing and actual distribution processing
 */
class DebugSalesServiceCacheCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'distribution:debug-cache 
                            {date : Date for testing (YYYY-MM-DD)}
                            {product_id : Product ID}
                            {distributor_ids : Comma-separated distributor IDs}
                            {--division-ids= : Comma-separated division IDs (optional)}
                            {--clear-cache : Clear cache before testing}';

    /**
     * The console command description.
     */
    protected $description = 'Debug SalesService cache key generation and instance differences between manual and factory creation';

    private SalesServiceFactoryInterface $salesServiceFactory;

    public function __construct(SalesServiceFactoryInterface $salesServiceFactory)
    {
        parent::__construct();
        $this->salesServiceFactory = $salesServiceFactory;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        // Temporarily disable logging to avoid permission issues
        config(['logging.default' => 'null']);

        $date = $this->argument('date');
        $productId = (int) $this->argument('product_id');
        $distributorIds = array_map('intval', explode(',', $this->argument('distributor_ids')));
        $divisionIds = $this->option('division-ids')
            ? array_map('intval', explode(',', $this->option('division-ids')))
            : [];

        if ($this->option('clear-cache')) {
            Cache::flush();
            $this->info('Cache cleared.');
        }

        $this->info("=== SalesService Cache Key Debug ===");
        $this->info("Date: {$date}");
        $this->info("Product ID: {$productId}");
        $this->info("Distributor IDs: " . implode(', ', $distributorIds));
        $this->info("Division IDs: " . implode(', ', $divisionIds));
        $this->newLine();

        // Test 1: Manual service creation (like Tinker)
        $this->info("1. MANUAL SERVICE CREATION (like Tinker)");
        $this->testManualServiceCreation($date, $productId, $distributorIds, $divisionIds);
        $this->newLine();

        // Test 2: Factory service creation (like distribution processing)
        $this->info("2. FACTORY SERVICE CREATION (like distribution processing)");
        $this->testFactoryServiceCreation($date, $productId, $distributorIds, $divisionIds);
        $this->newLine();

        // Test 3: Multiple factory instances
        $this->info("3. MULTIPLE FACTORY INSTANCES (cache key differences)");
        $this->testMultipleFactoryInstances($date, $productId, $distributorIds, $divisionIds);
        $this->newLine();

        // Test 4: Cache key analysis
        $this->info("4. CACHE KEY ANALYSIS");
        $this->analyzeCacheKeys($date, $productId, $distributorIds, $divisionIds);
        $this->newLine();

        // Test 5: Simulate actual distribution processing
        $this->info("5. SIMULATE ACTUAL DISTRIBUTION PROCESSING");
        $this->simulateDistributionProcessing($date, $productId, $distributorIds, $divisionIds);

        return 0;
    }

    private function testManualServiceCreation(string $date, int $productId, array $distributorIds, array $divisionIds): void
    {
        $this->line("Creating service manually (app(SalesServiceFactoryInterface::class)->createNormal())...");
        
        $service = app(SalesServiceFactoryInterface::class)->createNormal(DistributionType::STORES);
        
        $this->displayServiceInfo($service, "Manual");
        
        DB::enableQueryLog();
        $result = $service->getRatiosForDistribution($date, $productId, $distributorIds, $divisionIds);
        $queries = DB::getQueryLog();
        DB::disableQueryLog();
        
        $this->displayResults($result, $queries, "Manual Creation");
    }

    private function testFactoryServiceCreation(string $date, int $productId, array $distributorIds, array $divisionIds): void
    {
        $this->line("Creating service via factory (like SplitDistributionAlgorithm)...");
        
        $service = $this->salesServiceFactory->createForDistribution(SaleDistribution::NORMAL, DistributionType::STORES);
        
        $this->displayServiceInfo($service, "Factory");
        
        DB::enableQueryLog();
        $result = $service->getRatiosForDistribution($date, $productId, $distributorIds, $divisionIds);
        $queries = DB::getQueryLog();
        DB::disableQueryLog();
        
        $this->displayResults($result, $queries, "Factory Creation");
    }

    private function testMultipleFactoryInstances(string $date, int $productId, array $distributorIds, array $divisionIds): void
    {
        $this->line("Testing multiple factory instances to check cache key differences...");
        
        for ($i = 1; $i <= 3; $i++) {
            $this->line("--- Instance {$i} ---");
            
            $service = $this->salesServiceFactory->createForDistribution(SaleDistribution::NORMAL, DistributionType::STORES);
            
            $this->displayServiceInfo($service, "Instance {$i}");
            
            $result = $service->getRatiosForDistribution($date, $productId, $distributorIds, $divisionIds);
            
            $this->table(
                ['Metric', 'Value'],
                [
                    ['Results Count', $result->count()],
                    ['Total Percentage', $result->sum('percentage')],
                    ['Cache Hit/Miss', 'Check logs for cache behavior']
                ]
            );
        }
    }

    private function analyzeCacheKeys(string $date, int $productId, array $distributorIds, array $divisionIds): void
    {
        $this->line("Analyzing cache key generation differences...");
        
        // Create two different service instances
        $service1 = app(SalesServiceFactoryInterface::class)->createNormal(DistributionType::STORES);
        $service2 = $this->salesServiceFactory->createForDistribution(SaleDistribution::NORMAL, DistributionType::STORES);
        
        $this->table(
            ['Property', 'Service 1 (Manual)', 'Service 2 (Factory)', 'Same?'],
            [
                ['Object ID', spl_object_id($service1), spl_object_id($service2), spl_object_id($service1) === spl_object_id($service2) ? 'Yes' : 'No'],
                ['Class', get_class($service1), get_class($service2), get_class($service1) === get_class($service2) ? 'Yes' : 'No'],
                ['Instance Hash', spl_object_id($service1), spl_object_id($service2), 'Different by design'],
            ]
        );
        
        $this->warn("FINDING: Each service instance has a different spl_object_id, which creates different cache keys!");
        $this->warn("This means manual testing and distribution processing use separate cache entries.");
    }

    private function simulateDistributionProcessing(string $date, int $productId, array $distributorIds, array $divisionIds): void
    {
        $this->line("Simulating the exact distribution processing flow...");

        // Simulate SplitDistributionAlgorithm::getPrimaryDistributionRatios
        $this->line("--- Primary Distribution (90%) ---");
        $primaryService = $this->salesServiceFactory->createForDistribution(SaleDistribution::NORMAL, DistributionType::STORES);

        DB::enableQueryLog();
        $primaryRatios = $primaryService->getRatiosForDistribution($date, $productId, $distributorIds);
        $primaryQueries = DB::getQueryLog();
        DB::disableQueryLog();

        $this->displayResults($primaryRatios, $primaryQueries, "Primary Distribution (90%)");

        // Simulate SplitDistributionAlgorithm::getSecondaryDistributionRatios with division filter
        $this->line("--- Secondary Distribution (10%) ---");
        $secondaryService = $this->salesServiceFactory->createForDistribution(SaleDistribution::NORMAL, DistributionType::STORES);

        // Use the division IDs that would come from getDeepestDescendantIds
        $testDivisionIds = !empty($divisionIds) ? $divisionIds : [608,609,610,611,622,635,637,638,643,647,648,655,656,662];

        DB::enableQueryLog();
        $secondaryRatios = $secondaryService->getRatiosForDistribution($date, $productId, $distributorIds, $testDivisionIds);
        $secondaryQueries = DB::getQueryLog();
        DB::disableQueryLog();

        $this->displayResults($secondaryRatios, $secondaryQueries, "Secondary Distribution (10%)");

        // Compare the results
        $this->line("--- COMPARISON ---");
        $this->table(
            ['Distribution', 'Results Count', 'Total Percentage', 'Division Filter'],
            [
                ['Primary (90%)', $primaryRatios->count(), $primaryRatios->sum('percentage'), 'None'],
                ['Secondary (10%)', $secondaryRatios->count(), $secondaryRatios->sum('percentage'), count($testDivisionIds) . ' divisions'],
            ]
        );

        if ($primaryRatios->count() > 0 && $secondaryRatios->count() === 0) {
            $this->error("ISSUE FOUND: Primary distribution works but secondary distribution returns empty!");
            $this->warn("This suggests the division filter is causing the empty results.");
        } elseif ($primaryRatios->count() === 0) {
            $this->error("ISSUE FOUND: Even primary distribution returns empty results!");
            $this->warn("This suggests a fundamental issue with the query conditions.");
        } else {
            $this->info("Both primary and secondary distributions return results.");
        }
    }

    private function displayServiceInfo($service, string $label): void
    {
        $reflection = new \ReflectionClass($service);
        
        $saleDistributionProp = $reflection->getProperty('saleDistribution');
        $saleDistributionProp->setAccessible(true);
        $saleDistribution = $saleDistributionProp->getValue($service);
        
        $distributionTypeProp = $reflection->getProperty('distributionType');
        $distributionTypeProp->setAccessible(true);
        $distributionType = $distributionTypeProp->getValue($service);
        
        $selectionsProp = $reflection->getProperty('selections');
        $selectionsProp->setAccessible(true);
        $selections = $selectionsProp->getValue($service);
        
        $this->table(
            ['Property', 'Value'],
            [
                ['Label', $label],
                ['Object ID', spl_object_id($service)],
                ['Sale Distribution', $saleDistribution->name],
                ['Distribution Type', $distributionType?->value ?? 'NULL'],
                ['Selections Count', count($selections)],
                ['Class', get_class($service)]
            ]
        );
    }

    private function displayResults($result, array $queries, string $context): void
    {
        $this->table(
            ['Metric', 'Value'],
            [
                ['Context', $context],
                ['Results Count', $result->count()],
                ['Total Percentage', $result->sum('percentage')],
                ['Queries Executed', count($queries)],
                ['Cache Behavior', count($queries) > 0 ? 'Cache Miss (DB Query)' : 'Cache Hit (No DB Query)']
            ]
        );
        
        if ($result->count() > 0) {
            $this->info('Sample Results:');
            $this->table(
                ['Div ID', 'Line ID', 'Brick ID', 'Percentage'],
                $result->take(3)->map(fn($ratio) => [
                    $ratio->div_id,
                    $ratio->line_id,
                    $ratio->brick_id,
                    $ratio->percentage
                ])->toArray()
            );
        } else {
            $this->error('NO RESULTS FOUND!');
        }
        
        if (!empty($queries)) {
            $lastQuery = end($queries);
            $this->line("SQL: " . substr($lastQuery['query'], 0, 100) . "...");
        }
    }
}
