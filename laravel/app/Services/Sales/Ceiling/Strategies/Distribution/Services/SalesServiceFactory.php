<?php

namespace App\Services\Sales\Ceiling\Strategies\Distribution\Services;

use App\Services\Enums\SaleDistribution;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\SalesServiceFactoryInterface;
use App\Services\Sales\Ceiling\Strategies\Distribution\DistributionType;
use App\Services\SalesService;

/**
 * Factory for creating SalesService instances with proper configuration
 *
 * This factory encapsulates the creation of SalesService instances, ensuring
 * consistent configuration and proper dependency injection throughout the
 * distribution strategy system. It maintains Laravel Octane compatibility
 * by creating fresh instances for each request.
 */
class SalesServiceFactory implements SalesServiceFactoryInterface
{
    /**
     * Create a SalesService instance for distribution processing
     *
     * @param SaleDistribution $saleDistribution The sale distribution type (NORMAL, DIRECT)
     * @param DistributionType|null $distributionType The distribution type (PRIVATE_PHARMACY, STORES, LOCAL_CHAINS)
     * @return SalesService Fresh SalesService instance configured for the specified distribution
     */
    public function createForDistribution(
        SaleDistribution $saleDistribution,
        ?DistributionType $distributionType = null
    ): SalesService {
        // Use the static factory method from SalesService
        return SalesService::make($saleDistribution, $distributionType);
    }

    /**
     * Create a SalesService instance for normal distribution
     *
     * @param DistributionType|null $distributionType The distribution type
     * @return SalesService Fresh SalesService instance for normal distribution
     */
    public function createNormal(?DistributionType $distributionType = null): SalesService
    {
        return $this->createForDistribution(SaleDistribution::NORMAL, $distributionType);
    }

    /**
     * Create a SalesService instance for direct distribution
     *
     * @param DistributionType|null $distributionType The distribution type
     * @return SalesService Fresh SalesService instance for direct distribution
     */
    public function createDirect(?DistributionType $distributionType = null): SalesService
    {
        return $this->createForDistribution(SaleDistribution::DIRECT, $distributionType);
    }
}
