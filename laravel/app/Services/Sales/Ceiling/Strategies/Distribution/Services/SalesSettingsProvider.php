<?php

namespace App\Services\Sales\Ceiling\Strategies\Distribution\Services;

use App\SalesSetting;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\SettingsProviderInterface;
use Illuminate\Support\Facades\Cache;

class SalesSettingsProvider implements SettingsProviderInterface
{
    private const CACHE_TTL = 3600; // 1 hour

    /**
     * Get sales contribution settings
     *
     * @return array
     */
    public function getSalesContributionSettings(): array
    {
        return Cache::remember(
            'sales_contribution_base_on',
            self::CACHE_TTL,
            function () {
                $salesContribution = SalesSetting::where('key', 'sales_contribution_base_on')
                    ->value('value');

                return array_map("intval",explode(',', $salesContribution ?? ''));
            }
        );
    }

    /**
     * Get a specific setting value
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public function getSetting(string $key, $default = null)
    {
        return Cache::remember(
            "sales_setting_{$key}",
            self::CACHE_TTL,
            function () use ($key, $default) {
                return SalesSetting::where('key', $key)->value('value') ?? $default;
            }
        );
    }
}
