<?php

namespace App\Services\Sales\Ceiling\Strategies\Distribution\Contracts;

use App\Services\Enums\SaleDistribution;
use App\Services\Sales\Ceiling\Strategies\Distribution\DistributionType;
use App\Services\SalesService;

/**
 * Interface for creating SalesService instances with proper configuration
 *
 * This factory interface provides a clean abstraction for creating SalesService
 * instances with specific distribution types and sale distributions, ensuring
 * proper dependency injection and Laravel Octane compatibility.
 */
interface SalesServiceFactoryInterface
{
    /**
     * Create a SalesService instance for distribution processing
     *
     * @param SaleDistribution $saleDistribution The sale distribution type (NORMAL, DIRECT)
     * @param DistributionType|null $distributionType The distribution type (PRIVATE_PHARMACY, STORES, LOCAL_CHAINS)
     * @return SalesService Fresh SalesService instance configured for the specified distribution
     */
    public function createForDistribution(
        SaleDistribution $saleDistribution,
        ?DistributionType $distributionType = null
    ): SalesService;

    /**
     * Create a SalesService instance for normal distribution
     *
     * @param DistributionType|null $distributionType The distribution type
     * @return SalesService Fresh SalesService instance for normal distribution
     */
    public function createNormal(?DistributionType $distributionType = null): SalesService;

    /**
     * Create a SalesService instance for direct distribution
     *
     * @param DistributionType|null $distributionType The distribution type
     * @return SalesService Fresh SalesService instance for direct distribution
     */
    public function createDirect(?DistributionType $distributionType = null): SalesService;
}
