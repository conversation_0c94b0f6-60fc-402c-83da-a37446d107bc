<?php

namespace App\Services\Sales\Ceiling\Strategies\Distribution\Algorithms;

use App\LineDivision;
use App\Sale;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\ExcessDistributorInterface;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\SalesServiceFactoryInterface;
use App\Services\Sales\Ceiling\Strategies\Distribution\DistributionType;
use App\Services\Sales\Ceiling\Strategies\Distribution\Services\SaleDetailFactory;
use App\Services\Enums\SaleDistribution;
use App\Services\SalesService;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

/**
 * Split distribution algorithm that distributes excess sales using 90/10 split
 * (used by StoreStrategy)
 */
class SplitDistributionAlgorithm implements ExcessDistributorInterface
{
    private SalesService $salesService;
    private SaleDetailFactory $saleDetailFactory;
    private SalesServiceFactoryInterface $salesServiceFactory;
    private float $primaryPercentage;
    private float $secondaryPercentage;

    public function __construct(
        SalesService $salesService,
        SaleDetailFactory $saleDetailFactory,
        SalesServiceFactoryInterface $salesServiceFactory,
        float $primaryPercentage = 0.9,
        float $secondaryPercentage = 0.1
    ) {
        $this->salesService = $salesService;
        $this->saleDetailFactory = $saleDetailFactory;
        $this->salesServiceFactory = $salesServiceFactory;
        $this->primaryPercentage = $primaryPercentage;
        $this->secondaryPercentage = $secondaryPercentage;

        Log::debug('SplitDistributionAlgorithm: Initialized with split percentages', [
            'primary_percentage' => $this->primaryPercentage,
            'secondary_percentage' => $this->secondaryPercentage,
            'total_percentage' => $this->primaryPercentage + $this->secondaryPercentage
        ]);
    }

    /**
     * Distribute excess sale using 90/10 split distribution
     *
     * @param Sale $sale
     * @param array $salesContributionBaseOn
     * @param mixed $originalSale
     * @param DistributionType|null $distributionType
     * @return bool
     */
    public function distributeExcessSale(Sale $sale, array $salesContributionBaseOn,?Sale $originalSale = null, ?DistributionType $distributionType = null): bool
    {
        Log::info('SplitDistributionAlgorithm: Starting 90/10 split distribution', [
            'sale_id' => $sale->id,
            'sale_quantity' => $sale->quantity,
            'sale_value' => $sale->value,
            'sale_bonus' => $sale->bonus,
            'original_sale_id' => $originalSale?->id,
            'distribution_type' => $distributionType?->value,
            'sales_contribution_count' => count($salesContributionBaseOn),
            'primary_percentage' => $this->primaryPercentage,
            'secondary_percentage' => $this->secondaryPercentage
        ]);

        // Get primary distribution ratios (90%)
        Log::debug('SplitDistributionAlgorithm: Calculating primary distribution ratios', [
            'sale_id' => $sale->id,
            'percentage' => $this->primaryPercentage
        ]);

        $primaryRatios = $this->getPrimaryDistributionRatios($sale, $salesContributionBaseOn, $distributionType);

        Log::debug('SplitDistributionAlgorithm: Primary ratios calculated', [
            'sale_id' => $sale->id,
            'ratios_count' => $primaryRatios->count(),
            'total_percentage' => $primaryRatios->sum('percentage')
        ]);

        $primaryDetails = $this->saleDetailFactory->createDetailsFromRatios(
            $sale,
            $primaryRatios->toArray(),
            $this->primaryPercentage,
            $this->primaryPercentage,
            $this->primaryPercentage,
            $this->primaryPercentage
        );

        $primaryQuantitySum = array_sum(array_column($primaryDetails, 'quantity'));
        $primaryValueSum = array_sum(array_column($primaryDetails, 'value'));

        Log::debug('SplitDistributionAlgorithm: Primary details created', [
            'sale_id' => $sale->id,
            'details_count' => count($primaryDetails),
            'total_quantity' => $primaryQuantitySum,
            'total_value' => $primaryValueSum,
            'percentage_of_sale' => $this->primaryPercentage
        ]);

        // Get secondary distribution ratios (10%)
        Log::debug('SplitDistributionAlgorithm: Calculating secondary distribution ratios', [
            'sale_id' => $sale->id,
            'percentage' => $this->secondaryPercentage,
            'original_sale_id' => $originalSale?->id
        ]);

        $secondaryRatios = $this->getSecondaryDistributionRatios($sale, $originalSale, $salesContributionBaseOn, $distributionType);

        Log::debug('SplitDistributionAlgorithm: Secondary ratios calculated', [
            'sale_id' => $sale->id,
            'ratios_count' => $secondaryRatios->count(),
            'total_percentage' => $secondaryRatios->sum('percentage')
        ]);

        $secondaryDetails = $this->saleDetailFactory->createDetailsFromRatios(
            $sale,
            $secondaryRatios->toArray(),
            $this->secondaryPercentage,
            $this->secondaryPercentage,
            $this->secondaryPercentage,
            $this->secondaryPercentage
        );

        $secondaryQuantitySum = array_sum(array_column($secondaryDetails, 'quantity'));
        $secondaryValueSum = array_sum(array_column($secondaryDetails, 'value'));

        Log::debug('SplitDistributionAlgorithm: Secondary details created', [
            'sale_id' => $sale->id,
            'details_count' => count($secondaryDetails),
            'total_quantity' => $secondaryQuantitySum,
            'total_value' => $secondaryValueSum,
            'percentage_of_sale' => $this->secondaryPercentage
        ]);

        $allDetails = array_merge($primaryDetails, $secondaryDetails);
        $totalQuantitySum = $primaryQuantitySum + $secondaryQuantitySum;
        $totalValueSum = $primaryValueSum + $secondaryValueSum;

        $quantityMatch = abs($totalQuantitySum - $sale->quantity) < 0.01;
        $valueMatch = abs($totalValueSum - $sale->value) < 0.01;
        $hasDetails = count($allDetails) > 0;

        Log::info('SplitDistributionAlgorithm: Combined distribution details prepared', [
            'sale_id' => $sale->id,
            'total_details_count' => count($allDetails),
            'primary_details_count' => count($primaryDetails),
            'secondary_details_count' => count($secondaryDetails),
            'total_distributed_quantity' => $totalQuantitySum,
            'total_distributed_value' => $totalValueSum,
            'original_sale_quantity' => $sale->quantity,
            'original_sale_value' => $sale->value,
            'quantity_match' => $quantityMatch,
            'value_match' => $valueMatch,
            'has_details' => $hasDetails
        ]);

        // Enhanced validation with detailed error reporting
        if (!$hasDetails) {
            Log::error('SplitDistributionAlgorithm: Distribution failed - no details created', [
                'sale_id' => $sale->id,
                'primary_ratios_count' => $primaryRatios->count(),
                'secondary_ratios_count' => $secondaryRatios->count(),
                'primary_details_count' => count($primaryDetails),
                'secondary_details_count' => count($secondaryDetails),
                'reason' => 'No distribution ratios resulted in sale details',
                'possible_causes' => [
                    'No valid distribution ratios found for the given date/product/distributors',
                    'Division IDs from original sale may not have valid descendants',
                    'Distribution type constraints may be too restrictive'
                ]
            ]);
            return false;
        }

        if (!$quantityMatch) {
            Log::error('SplitDistributionAlgorithm: Distribution failed - quantity mismatch', [
                'sale_id' => $sale->id,
                'expected_quantity' => $sale->quantity,
                'calculated_quantity' => $totalQuantitySum,
                'primary_quantity' => $primaryQuantitySum,
                'secondary_quantity' => $secondaryQuantitySum,
                'difference' => abs($totalQuantitySum - $sale->quantity),
                'tolerance' => 0.01,
                'reason' => 'Calculated distribution quantities do not match sale quantity'
            ]);
            return false;
        }

        // Attempt to insert details with error handling
        try {
            $result = $this->saleDetailFactory->insertDetails($allDetails);
        } catch (\Throwable $e) {
            Log::error('SplitDistributionAlgorithm: Failed to insert sale details', [
                'sale_id' => $sale->id,
                'details_count' => count($allDetails),
                'error_message' => $e->getMessage(),
                'error_trace' => $e->getTraceAsString()
            ]);
            return false;
        }

        if (!$result) {
            Log::error('SplitDistributionAlgorithm: Sale details insertion failed', [
                'sale_id' => $sale->id,
                'details_count' => count($allDetails),
                'reason' => 'SaleDetailFactory::insertDetails returned false'
            ]);
            return false;
        }

        Log::info('SplitDistributionAlgorithm: Distribution completed successfully', [
            'sale_id' => $sale->id,
            'success' => $result,
            'details_inserted' => count($allDetails),
            'primary_details' => count($primaryDetails),
            'secondary_details' => count($secondaryDetails),
            'total_quantity_distributed' => $totalQuantitySum,
            'validation_passed' => $result && $hasDetails && $quantityMatch
        ]);

        return $result;
    }

    /**
     * Calculate excess quantity for distribution
     * Updated to handle null limits from LEFT JOIN (STORES distribution type)
     *
     * @param mixed $ceilingSale
     * @return float
     */
    public function calculateExcessQuantity($ceilingSale): float
    {
        // Calculate the actual excess above the limit
        $limit = 0;
        $totalUnits = $ceilingSale->number_of_units ?? 0;

        if ($totalUnits > 0) {
            $limit = $ceilingSale->limit ?? 0;
        }

        if ($totalUnits < 0) {
            $limit = $ceilingSale->negative_limit ?? 0;
        }

        $excessQuantity = $totalUnits - $limit;

        Log::debug('SplitDistributionAlgorithm: Calculated excess quantity', [
            'ceiling_sale_id' => $ceilingSale->id ?? 'unknown',
            'total_units' => $totalUnits,
            'limit' => $limit,
            'excess_quantity' => $excessQuantity,
            'is_positive_units' => $totalUnits > 0,
            'is_negative_units' => $totalUnits < 0,
            'limit_type' => $totalUnits > 0 ? 'positive_limit' : ($totalUnits < 0 ? 'negative_limit' : 'no_limit')
        ]);

        return $excessQuantity;
    }

    /**
     * Get primary distribution ratios (90% portion)
     *
     * @param Sale $sale
     * @param array $salesContributionBaseOn
     * @param DistributionType|null $distributionType
     * @return Collection
     */
    private function getPrimaryDistributionRatios(Sale $sale, array $salesContributionBaseOn, ?DistributionType $distributionType = null): Collection
    {
        Log::debug('SplitDistributionAlgorithm: Getting primary distribution ratios', [
            'sale_id' => $sale->id,
            'sale_date' => $sale->date,
            'product_id' => $sale->product_id,
            'distribution_type' => $distributionType?->value,
            'sales_contribution_base_on' => $salesContributionBaseOn,
            'percentage' => $this->primaryPercentage
        ]);

        // Create SalesService with the appropriate DistributionType using factory
        $salesService = $distributionType
             ? $this->salesServiceFactory->createForDistribution(SaleDistribution::NORMAL, $distributionType)
             : $this->salesService;

        $ratios = $salesService
            ->getRatiosForDistribution(
                $sale->date,
                $sale->product_id,
                $salesContributionBaseOn
            );

        Log::debug('SplitDistributionAlgorithm: Primary ratios retrieved', [
            'sale_id' => $sale->id,
            'ratios_count' => $ratios->count(),
            'total_percentage' => $ratios->sum('percentage'),
            'distribution_type' => $distributionType?->value ?? 'default'
        ]);

        if ($ratios->count() === 0) {
            Log::warning('SplitDistributionAlgorithm: No primary distribution ratios found', [
                'sale_id' => $sale->id,
                'sale_date' => $sale->date,
                'product_id' => $sale->product_id,
                'distributor_ids' => $salesContributionBaseOn,
                'distribution_type' => $distributionType?->value ?? 'default'
            ]);
        }

        return $ratios;
    }

    /**
     * Get secondary distribution ratios (10% portion)
     *
     * @param Sale $sale
     * @param mixed $originalSale
     * @param array $salesContributionBaseOn
     * @param DistributionType|null $distributionType
     * @return Collection
     */
    private function getSecondaryDistributionRatios(Sale $sale,?Sale $originalSale, array $salesContributionBaseOn, ?DistributionType $distributionType = null): Collection
    {
        Log::debug('SplitDistributionAlgorithm: Getting secondary distribution ratios', [
            'sale_id' => $sale->id,
            'original_sale_id' => $originalSale?->id,
            'distribution_type' => $distributionType?->value,
            'percentage' => $this->secondaryPercentage
        ]);

        if (!$originalSale) {
            Log::warning('SplitDistributionAlgorithm: No original sale provided, falling back to primary distribution', [
                'sale_id' => $sale->id
            ]);
            // Fallback to primary distribution if no original sale
            return $this->getPrimaryDistributionRatios($sale, $salesContributionBaseOn, $distributionType);
        }

        $divisionIds = $this->getDeepestDescendantIds($originalSale);

        Log::debug('SplitDistributionAlgorithm: Division IDs extracted for secondary distribution', [
            'sale_id' => $sale->id,
            'original_sale_id' => $originalSale->id,
            'division_ids_count' => count($divisionIds),
            'division_ids' => $divisionIds
        ]);

        // Create SalesService with the appropriate DistributionType using factory
        $salesService = $distributionType
            ? $this->salesServiceFactory->createForDistribution(SaleDistribution::NORMAL, $distributionType)
            : $this->salesService;

        $ratios = $salesService
            ->getRatiosForDistribution(
                $sale->date,
                $sale->product_id,
                $salesContributionBaseOn,
                $divisionIds
            );

        Log::debug('SplitDistributionAlgorithm: Secondary ratios retrieved', [
            'sale_id' => $sale->id,
            'ratios_count' => $ratios->count(),
            'total_percentage' => $ratios->sum('percentage'),
            'division_ids_used' => count($divisionIds)
        ]);

        if ($ratios->count() === 0) {
            Log::warning('SplitDistributionAlgorithm: No secondary distribution ratios found', [
                'sale_id' => $sale->id,
                'original_sale_id' => $originalSale?->id,
                'division_ids' => $divisionIds,
                'division_ids_count' => count($divisionIds),
                'distribution_type' => $distributionType?->value ?? 'default'
            ]);
        }

        return $ratios;
    }

    /**
     * Get deepest descendant division IDs from original sale
     *
     * @param mixed $originalSale
     * @return array
     */
    private function getDeepestDescendantIds(Sale $originalSale): array
    {
        Log::debug('SplitDistributionAlgorithm: Getting deepest descendant division IDs', [
            'original_sale_id' => $originalSale->id,
            'has_details' => !is_null($originalSale->details)
        ]);

        // Check if details exist and is not null
        if (!$originalSale->details) {
            Log::warning('SplitDistributionAlgorithm: No sale details found for original sale', [
                'original_sale_id' => $originalSale->id
            ]);
            return [];
        }

        $divisionIds = $originalSale->details->pluck('div_id')->toArray();

        if (empty($divisionIds)) {
            Log::warning('SplitDistributionAlgorithm: No division IDs found in sale details', [
                'original_sale_id' => $originalSale->id,
                'details_count' => $originalSale->details->count()
            ]);
            return [];
        }

        Log::debug('SplitDistributionAlgorithm: Division IDs extracted from sale details', [
            'original_sale_id' => $originalSale->id,
            'division_ids' => $divisionIds,
            'division_ids_count' => count($divisionIds)
        ]);

        $descendantIds = LineDivision::whereIn('id', $divisionIds)
            ->get()
            ->map(function (LineDivision $lineDivision) {
                return $lineDivision->getDeepestDescendantsAtLevel(2)->pluck('id')->toArray();
            })
            ->flatten()
            ->unique()
            ->values()
            ->toArray();

        Log::debug('SplitDistributionAlgorithm: Deepest descendant IDs calculated', [
            'original_sale_id' => $originalSale->id,
            'input_division_ids_count' => count($divisionIds),
            'descendant_ids_count' => count($descendantIds),
            'descendant_ids' => $descendantIds
        ]);

        return $descendantIds;
    }
}
