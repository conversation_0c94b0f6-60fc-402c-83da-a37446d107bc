<?php

namespace App\Services\Sales\Ceiling\Strategies\Distribution;

use App\Sale;
use App\Services\Sales\Ceiling\Strategies\Distribution\Algorithms\SimpleDistributionAlgorithm;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\ExcessDistributorInterface;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\LimitCalculatorInterface;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\SettingsProviderInterface;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\TransactionManagerInterface;
use App\Services\Sales\Ceiling\Strategies\Distribution\Services\SaleCreator;
use App\Services\Sales\Ceiling\Strategies\Distribution\Services\SaleDetailFactory;
use App\Services\SalesService;
use Illuminate\Support\Facades\Log;

/**
 * Refactored Private Pharmacy Distribution Strategy
 *
 * This strategy uses simple 100% distribution for excess sales.
 * It follows SOLID principles and uses dependency injection.
 */
class PrivatePharmacyStrategy extends AbstractDistributionStrategy
{
    private ExcessDistributorInterface $excessDistributor;

    public function __construct(
        TransactionManagerInterface $transactionManager,
        SettingsProviderInterface $settingsProvider,
        LimitCalculatorInterface $limitCalculator,
        SaleDetailFactory $saleDetailFactory,
        SaleCreator $saleCreator,
        SalesService $salesService,
        SimpleDistributionAlgorithm $excessDistributor
    ) {
        parent::__construct(
            $transactionManager,
            $settingsProvider,
            $limitCalculator,
            $saleDetailFactory,
            $saleCreator,
            $salesService
        );

        $this->excessDistributor = $excessDistributor;
    }




    /**
     * Override to ensure limited sale is properly attached to mapping
     *
     * @param mixed $ceilingSale
     * @param Sale $originalSale
     * @return bool
     */
    protected function createLimitedSaleDistribution($ceilingSale, Sale $originalSale): bool
    {
        if ($originalSale->quantity == 0) {
            return false;
        }

        $limitQuantity = $this->limitCalculator->calculateLimit($ceilingSale);
        $limitedSale = $this->saleCreator->createLimitedSale($ceilingSale, $originalSale, $limitQuantity);

        // Attach the limited sale to the mapping for proper referential integrity
        $this->saleCreator->attachMapping($limitedSale, $ceilingSale->mapping_id);

        return $this->saleDetailFactory->createLimitedSaleDetails($originalSale, $limitedSale, $ceilingSale);
    }

    /**
     * Create and distribute excess sale using simple distribution
     *
     * @param mixed $ceilingSale
     * @param Sale $originalSale
     * @param array $salesContributionBaseOn
     * @return bool
     */
    protected function createAndDistributeExcessSale($ceilingSale, Sale $originalSale, array $salesContributionBaseOn): bool
    {
        $excessQuantity = $this->excessDistributor->calculateExcessQuantity($ceilingSale);

        $excessSale = $this->saleCreator->createExcessSale($ceilingSale, $excessQuantity);
        $excessSale = $this->saleCreator->loadRelationships($excessSale);

        $distributionSuccess = $this->excessDistributor->distributeExcessSale(
            $excessSale,
            $salesContributionBaseOn,
            $originalSale,
            $this->getDistributionType()
        );

        if ($distributionSuccess) {
            // Additional validation: Verify sale details were actually created
            $excessSale->refresh(); // Reload to get latest data
            $detailsCount = $excessSale->details()->count();
            $totalDetailQuantity = $excessSale->details()->sum('quantity');
            $quantityMatch = abs($totalDetailQuantity - $excessSale->quantity) < 0.01;

            if ($detailsCount > 0 && $quantityMatch) {
                $this->saleCreator->attachMapping($excessSale, $ceilingSale->mapping_id);
            } else {
                Log::error('PrivatePharmacyStrategy: Distribution validation failed - sale details missing or quantity mismatch', [
                    'ceiling_sale_id' => $ceilingSale->id ?? 'unknown',
                    'sale_id' => $excessSale->id,
                    'details_count' => $detailsCount,
                    'expected_quantity' => $excessSale->quantity,
                    'actual_detail_quantity' => $totalDetailQuantity,
                    'quantity_match' => $quantityMatch
                ]);
                $distributionSuccess = false;
            }
        }

        return $distributionSuccess;
    }

    /**
     * Get the DistributionType for this strategy
     *
     * @return DistributionType
     */
    protected function getDistributionType(): DistributionType
    {
        return DistributionType::PRIVATE_PHARMACY;
    }
}
