<?php

namespace App\Providers;

use App\Services\Structure\Repositories\LineDivisionRepository;
use App\Services\Structure\Repositories\LineDivisionRepositoryInterface;
use App\Services\Sales\Ceiling\Strategies\Distribution\Algorithms\{HierarchicalChainDistributionAlgorithm,
    HierarchicalDistributionService,
    SimpleDistributionAlgorithm,
    SplitDistributionAlgorithm};
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\{
    TransactionManagerInterface,
    SettingsProviderInterface,
    LimitCalculatorInterface,
    SaleRepositoryInterface,
    SalesServiceFactoryInterface
};
use App\Services\Sales\Ceiling\Strategies\Distribution\Services\{
    TransactionManager,
    SalesSettingsProvider,
    LimitCalculator,
    SaleDetailFactory,
    SaleCreator,
    SaleRepository,
    SalesServiceFactory
};
use App\Services\Sales\Ceiling\Strategies\Distribution\{
    DistributionStrategyFactory,
    DistributionType,
    PrivatePharmacyStrategy,
    StoreStrategy,
    LocalChainStrategy
};
use App\Services\Enums\SaleDistribution;
use Illuminate\Support\ServiceProvider;

/**
 * Service Provider for Distribution Strategy dependencies
 *
 * This provider registers all the services and dependencies needed
 * for the refactored distribution strategy system.
 */
class DistributionStrategyServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register(): void
    {
        // Register core interfaces to implementations
        $this->registerCoreInterfaces();

        // Register service factories (not singletons for stateful services)
        $this->registerServiceFactories();

        // Register distribution algorithms with proper DI
        $this->registerDistributionAlgorithms();

        // Register strategies with clean dependency injection
        $this->registerStrategies();
    }

    /**
     * Register core interfaces to their implementations
     *
     * @return void
     */
    private function registerCoreInterfaces(): void
    {
        $this->app->bind(TransactionManagerInterface::class, TransactionManager::class);
        $this->app->bind(SettingsProviderInterface::class, SalesSettingsProvider::class);
        $this->app->bind(LimitCalculatorInterface::class, LimitCalculator::class);
        $this->app->bind(SaleRepositoryInterface::class, SaleRepository::class);
        $this->app->bind(LineDivisionRepositoryInterface::class, LineDivisionRepository::class);

        // Register SalesServiceFactory interface and implementation
        $this->app->bind(SalesServiceFactoryInterface::class, SalesServiceFactory::class);
    }

    /**
     * Register service factories with proper lifecycle management
     *
     * @return void
     */
    private function registerServiceFactories(): void
    {
        // Register stateless services as singletons
        $this->app->singleton(SaleDetailFactory::class);
        $this->app->singleton(SaleCreator::class);
        $this->app->singleton(DistributionStrategyFactory::class);

        // Register SalesService instances as factories (not singletons)
        // This ensures fresh instances are created for each request in Octane
        $this->app->bind('SalesService.Normal', function ($app) {
            return $app->make(SalesServiceFactoryInterface::class)
                       ->createNormal();
        });

        $this->app->bind('SalesService.Direct', function ($app) {
            return $app->make(SalesServiceFactoryInterface::class)
                       ->createDirect();
        });
    }

    /**
     * Register distribution algorithms with proper dependency injection
     *
     * @return void
     */
    private function registerDistributionAlgorithms(): void
    {
        // Register distribution algorithms with factory-created SalesService instances
        $this->app->bind(SimpleDistributionAlgorithm::class, function ($app) {
            return new SimpleDistributionAlgorithm(
                $app->make(SalesServiceFactoryInterface::class)
                    ->createForDistribution(SaleDistribution::NORMAL, DistributionType::PRIVATE_PHARMACY),
                $app->make(SaleDetailFactory::class),
                $app->make(SalesServiceFactoryInterface::class)
            );
        });

        $this->app->bind(SplitDistributionAlgorithm::class, function ($app) {
            return new SplitDistributionAlgorithm(
                $app->make(SalesServiceFactoryInterface::class)
                    ->createForDistribution(SaleDistribution::NORMAL, DistributionType::STORES),
                $app->make(SaleDetailFactory::class),
                $app->make(SalesServiceFactoryInterface::class),
                0.9, // Primary percentage
                0.1  // Secondary percentage
            );
        });

        $this->app->bind(HierarchicalChainDistributionAlgorithm::class, function ($app) {
            return new HierarchicalChainDistributionAlgorithm(
                $app->make(SalesServiceFactoryInterface::class)
                    ->createForDistribution(SaleDistribution::NORMAL, DistributionType::LOCAL_CHAINS),
                $app->make(SaleDetailFactory::class),
                $app->make(HierarchicalDistributionService::class),
                $app->make(SalesServiceFactoryInterface::class)
            );
        });
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot(): void
    {
        // Any bootstrapping logic can go here
    }

    /**
     * Register individual strategy classes with proper dependency injection
     *
     * @return void
     */
    private function registerStrategies(): void
    {
        $this->app->bind(PrivatePharmacyStrategy::class, function ($app) {
            return new PrivatePharmacyStrategy(
                $app->make(TransactionManagerInterface::class),
                $app->make(SettingsProviderInterface::class),
                $app->make(LimitCalculatorInterface::class),
                $app->make(SaleDetailFactory::class),
                $app->make(SaleCreator::class),
                $app->make(SalesServiceFactoryInterface::class)
                    ->createForDistribution(SaleDistribution::NORMAL, DistributionType::PRIVATE_PHARMACY),
                $app->make(SimpleDistributionAlgorithm::class)
            );
        });

        $this->app->bind(StoreStrategy::class, function ($app) {
            return new StoreStrategy(
                $app->make(TransactionManagerInterface::class),
                $app->make(SettingsProviderInterface::class),
                $app->make(LimitCalculatorInterface::class),
                $app->make(SaleDetailFactory::class),
                $app->make(SaleCreator::class),
                $app->make(SalesServiceFactoryInterface::class)
                    ->createForDistribution(SaleDistribution::NORMAL, DistributionType::STORES),
                $app->make(SplitDistributionAlgorithm::class)
            );
        });

        $this->app->bind(LocalChainStrategy::class, function ($app) {
            return new LocalChainStrategy(
                $app->make(TransactionManagerInterface::class),
                $app->make(SettingsProviderInterface::class),
                $app->make(LimitCalculatorInterface::class),
                $app->make(SaleDetailFactory::class),
                $app->make(SaleCreator::class),
                $app->make(SalesServiceFactoryInterface::class)
                    ->createForDistribution(SaleDistribution::NORMAL, DistributionType::LOCAL_CHAINS),
                $app->make(HierarchicalChainDistributionAlgorithm::class)
            );
        });
    }
}
